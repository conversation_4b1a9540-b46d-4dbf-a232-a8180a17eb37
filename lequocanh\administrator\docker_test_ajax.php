<?php
/**
 * Docker Terminal Test Script for AJAX Endpoints
 * Run: docker exec -it [container_name] php /var/www/html/administrator/docker_test_ajax.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once __DIR__ . '/elements_LQA/mod/database.php';
require_once __DIR__ . '/elements_LQA/mod/thuoctinhCls.php';

class AjaxTester {
    private $baseUrl;
    private $testResults = [];
    
    public function __construct() {
        $this->baseUrl = __DIR__;
        echo "🐳 DOCKER AJAX TEST - THUỘC TÍNH\n";
        echo "===============================\n\n";
    }
    
    public function runAllTests() {
        $this->testThuoctinhUpdateEndpoint();
        $this->testThuoctinhActEndpoint();
        $this->testFormDataProcessing();
        $this->printSummary();
    }
    
    private function testThuoctinhUpdateEndpoint() {
        echo "📝 Testing thuoctinhUpdate.php endpoint...\n";
        
        try {
            // Get test data
            $thuoctinhObj = new ThuocTinh();
            $data = $thuoctinhObj->thuoctinhGetAll();
            
            if ($data && count($data) > 0) {
                $testId = $data[0]->idThuocTinh;
                
                // Simulate POST request to thuoctinhUpdate.php
                $_POST['idThuocTinh'] = $testId;
                
                ob_start();
                include __DIR__ . '/elements_LQA/mthuoctinh/thuoctinhUpdate.php';
                $output = ob_get_clean();
                
                if (!empty($output)) {
                    $this->addResult("thuoctinhUpdate.php loads", true, "Generated " . strlen($output) . " bytes of HTML");
                    
                    // Check if form elements are present
                    if (strpos($output, 'name="tenThuocTinh"') !== false) {
                        $this->addResult("Form field: tenThuocTinh", true, "Field found in output");
                    } else {
                        $this->addResult("Form field: tenThuocTinh", false, "Field not found");
                    }
                    
                    if (strpos($output, 'name="ghiChu"') !== false) {
                        $this->addResult("Form field: ghiChu", true, "Field found in output");
                    } else {
                        $this->addResult("Form field: ghiChu", false, "Field not found");
                    }
                    
                    if (strpos($output, 'id="update-form"') !== false) {
                        $this->addResult("Form ID", true, "update-form ID found");
                    } else {
                        $this->addResult("Form ID", false, "update-form ID not found");
                    }
                    
                } else {
                    $this->addResult("thuoctinhUpdate.php loads", false, "No output generated");
                }
                
                // Clean up
                unset($_POST['idThuocTinh']);
                
            } else {
                $this->addResult("thuoctinhUpdate.php test", false, "No test data available");
            }
            
        } catch (Exception $e) {
            $this->addResult("thuoctinhUpdate.php test", false, "Error: " . $e->getMessage());
        }
        echo "\n";
    }
    
    private function testThuoctinhActEndpoint() {
        echo "⚡ Testing thuoctinhAct.php endpoint...\n";
        
        try {
            // Get test data
            $thuoctinhObj = new ThuocTinh();
            $data = $thuoctinhObj->thuoctinhGetAll();
            
            if ($data && count($data) > 0) {
                $testRecord = $data[0];
                $testId = $testRecord->idThuocTinh;
                $originalName = $testRecord->tenThuocTinh;
                $originalNote = $testRecord->ghiChu;
                
                // Simulate update request
                $_GET['reqact'] = 'updatethuoctinh';
                $_POST['idThuocTinh'] = $testId;
                $_POST['tenThuocTinh'] = $originalName . ' [AJAX TEST]';
                $_POST['ghiChu'] = $originalNote . ' [TESTED]';
                
                echo "   Testing with ID: $testId\n";
                echo "   Test name: " . $_POST['tenThuocTinh'] . "\n";
                
                ob_start();
                include __DIR__ . '/elements_LQA/mthuoctinh/thuoctinhAct.php';
                $output = ob_get_clean();
                
                if (!empty($output)) {
                    // Try to decode JSON response
                    $response = json_decode($output, true);
                    
                    if ($response !== null) {
                        $this->addResult("JSON response format", true, "Valid JSON returned");
                        
                        if (isset($response['success'])) {
                            if ($response['success']) {
                                $this->addResult("Update operation", true, $response['message'] ?? 'Success');
                                
                                // Restore original data
                                $_POST['tenThuocTinh'] = $originalName;
                                $_POST['ghiChu'] = $originalNote;
                                
                                ob_start();
                                include __DIR__ . '/elements_LQA/mthuoctinh/thuoctinhAct.php';
                                $restoreOutput = ob_get_clean();
                                
                                $restoreResponse = json_decode($restoreOutput, true);
                                if ($restoreResponse && $restoreResponse['success']) {
                                    $this->addResult("Data restoration", true, "Original data restored");
                                } else {
                                    $this->addResult("Data restoration", false, "Failed to restore data");
                                }
                                
                            } else {
                                $this->addResult("Update operation", false, $response['message'] ?? 'Unknown error');
                            }
                        } else {
                            $this->addResult("Response structure", false, "Missing 'success' field");
                        }
                    } else {
                        $this->addResult("JSON response format", false, "Invalid JSON: " . substr($output, 0, 100));
                    }
                } else {
                    $this->addResult("thuoctinhAct.php response", false, "No output generated");
                }
                
                // Clean up
                unset($_GET['reqact']);
                unset($_POST['idThuocTinh']);
                unset($_POST['tenThuocTinh']);
                unset($_POST['ghiChu']);
                
            } else {
                $this->addResult("thuoctinhAct.php test", false, "No test data available");
            }
            
        } catch (Exception $e) {
            $this->addResult("thuoctinhAct.php test", false, "Error: " . $e->getMessage());
        }
        echo "\n";
    }
    
    private function testFormDataProcessing() {
        echo "📋 Testing Form Data Processing...\n";
        
        // Test parameter extraction
        $_POST['idThuocTinh'] = '123';
        $_GET['id'] = '456';
        $_REQUEST['idThuocTinh'] = '789';
        
        // Test the parameter extraction logic from thuoctinhUpdate.php
        $idThuocTinh = $_POST['idThuocTinh'] ?? $_GET['idThuocTinh'] ?? $_REQUEST['idThuocTinh'] ?? $_REQUEST['id'] ?? $_GET['id'] ?? null;
        
        if ($idThuocTinh === '123') {
            $this->addResult("Parameter extraction", true, "POST parameter correctly prioritized");
        } else {
            $this->addResult("Parameter extraction", false, "Parameter extraction logic failed");
        }
        
        // Test with only GET parameter
        unset($_POST['idThuocTinh']);
        $idThuocTinh = $_POST['idThuocTinh'] ?? $_GET['idThuocTinh'] ?? $_REQUEST['idThuocTinh'] ?? $_REQUEST['id'] ?? $_GET['id'] ?? null;
        
        if ($idThuocTinh === '456') {
            $this->addResult("GET fallback", true, "GET parameter used when POST not available");
        } else {
            $this->addResult("GET fallback", false, "GET fallback failed");
        }
        
        // Clean up
        unset($_POST['idThuocTinh']);
        unset($_GET['id']);
        unset($_REQUEST['idThuocTinh']);
        
        echo "\n";
    }
    
    private function addResult($test, $success, $message) {
        $this->testResults[] = [
            'test' => $test,
            'success' => $success,
            'message' => $message
        ];
        
        $icon = $success ? "✅" : "❌";
        echo "   $icon $test: $message\n";
    }
    
    private function printSummary() {
        echo "📊 AJAX TEST SUMMARY\n";
        echo "===================\n";
        
        $total = count($this->testResults);
        $passed = array_filter($this->testResults, function($result) {
            return $result['success'];
        });
        $passedCount = count($passed);
        $failedCount = $total - $passedCount;
        
        echo "Total tests: $total\n";
        echo "✅ Passed: $passedCount\n";
        echo "❌ Failed: $failedCount\n";
        echo "Success rate: " . round(($passedCount / $total) * 100, 1) . "%\n\n";
        
        if ($failedCount > 0) {
            echo "❌ FAILED TESTS:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "   - " . $result['test'] . ": " . $result['message'] . "\n";
                }
            }
            echo "\n";
        }
        
        // Overall status
        if ($failedCount === 0) {
            echo "🎉 ALL AJAX TESTS PASSED! Update functionality should work correctly.\n";
        } else if ($failedCount <= 2) {
            echo "⚠️  MOSTLY WORKING with minor issues. Check failed tests above.\n";
        } else {
            echo "🚨 MULTIPLE AJAX ISSUES DETECTED. Please fix failed tests before using.\n";
        }
        
        echo "\n🐳 AJAX test completed at " . date('Y-m-d H:i:s') . "\n";
    }
}

// Run tests
try {
    $tester = new AjaxTester();
    $tester->runAllTests();
} catch (Exception $e) {
    echo "🚨 FATAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
