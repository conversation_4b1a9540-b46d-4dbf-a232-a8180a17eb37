2025-04-09 12:28:47 - Update parameters:
idhanghoa: 45 (type: integer)
idThuocTinh: 8 (type: integer)
tenThuocTinhHH: 6GB, 8GB (type: string)
idThuocTinhHH: 31 (type: integer)
2025-04-09 12:28:47 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 45
    [1] => 8
    [2] => 6GB, 8GB
    [3] => 31
)

2025-04-09 12:28:47 - Success, rows affected: 1
2025-04-09 12:28:55 - Update parameters:
idhanghoa: 45 (type: integer)
idThuocTinh: 8 (type: integer)
tenThuocTinhHH: 6GB, 8GB (type: string)
idThuocTinhHH: 31 (type: integer)
2025-04-09 12:28:55 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 45
    [1] => 8
    [2] => 6GB, 8GB
    [3] => 31
)

2025-04-09 12:28:55 - Success, rows affected: 0
2025-04-09 12:29:10 - Update parameters:
idhanghoa: 57 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen Phantom, Kem Cream, Xanh Botanic, Tím Lila (type: string)
idThuocTinhHH: 35 (type: integer)
2025-04-09 12:29:10 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 57
    [1] => 7
    [2] => Đen Phantom, Kem Cream, Xanh Botanic, Tím Lila
    [3] => 35
)

2025-04-09 12:29:10 - Success, rows affected: 1
2025-04-09 12:40:14 - Update parameters:
idhanghoa: 57 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen Phantom, Kem Cream, Xanh Botanic, Tím Lilac (type: string)
idThuocTinhHH: 35 (type: integer)
2025-04-09 12:40:14 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 57
    [1] => 7
    [2] => Đen Phantom, Kem Cream, Xanh Botanic, Tím Lilac
    [3] => 35
)

2025-04-09 12:40:14 - Success, rows affected: 1
2025-04-09 12:57:37 - Update parameters:
idhanghoa: 57 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen Phantom, Kem Cream, Xanh Botanic, Tím Lila (type: string)
idThuocTinhHH: 35 (type: integer)
2025-04-09 12:57:37 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 57
    [1] => 7
    [2] => Đen Phantom, Kem Cream, Xanh Botanic, Tím Lila
    [3] => 35
)

2025-04-09 12:57:37 - Success, rows affected: 1
2025-04-09 13:11:28 - Update parameters:
idhanghoa: 57 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen Phantom, Kem Cream, Xanh Botanic, Tím Lil (type: string)
idThuocTinhHH: 35 (type: integer)
2025-04-09 13:11:28 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 57
    [1] => 7
    [2] => Đen Phantom, Kem Cream, Xanh Botanic, Tím Lil
    [3] => 35
)

2025-04-09 13:11:28 - Success, rows affected: 1
2025-04-09 13:12:05 - Update parameters:
idhanghoa: 57 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen Phantom, Kem Cream, Xanh Botanic, Tím Lil (type: string)
idThuocTinhHH: 35 (type: integer)
2025-04-09 13:12:05 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 57
    [1] => 7
    [2] => Đen Phantom, Kem Cream, Xanh Botanic, Tím Lil
    [3] => 35
)

2025-04-09 13:12:05 - Success, rows affected: 0
2025-04-09 13:33:51 - Update parameters:
idhanghoa: 57 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen Phantom, Kem Cream, Xanh Botanic, Tím Lilac (type: string)
idThuocTinhHH: 35 (type: integer)
2025-04-09 13:33:51 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 57
    [1] => 7
    [2] => Đen Phantom, Kem Cream, Xanh Botanic, Tím Lilac
    [3] => 35
)

2025-04-09 13:33:53 - Success, rows affected: 1
2025-04-09 13:33:53 - Update parameters:
idhanghoa: 57 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen Phantom, Kem Cream, Xanh Botanic, Tím Lilac (type: string)
idThuocTinhHH: 35 (type: integer)
2025-04-09 13:33:53 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 57
    [1] => 7
    [2] => Đen Phantom, Kem Cream, Xanh Botanic, Tím Lilac
    [3] => 35
)

2025-04-09 13:33:53 - Success, rows affected: 0
2025-04-09 13:40:20 - Update parameters:
idhanghoa: 57 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen Phantom, Kem Cream, Xanh Botanic, Tím Lila (type: string)
idThuocTinhHH: 35 (type: integer)
2025-04-09 13:40:20 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 57
    [1] => 7
    [2] => Đen Phantom, Kem Cream, Xanh Botanic, Tím Lila
    [3] => 35
)

2025-04-09 13:40:20 - Success, rows affected: 1
2025-04-09 13:47:51 - Update parameters:
idhanghoa: 57 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen Phantom, Kem Cream, Xanh Botanic, Tím Lilac (type: string)
idThuocTinhHH: 35 (type: integer)
2025-04-09 13:47:51 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 57
    [1] => 7
    [2] => Đen Phantom, Kem Cream, Xanh Botanic, Tím Lilac
    [3] => 35
)

2025-04-09 13:47:51 - Success, rows affected: 1
2025-04-12 05:59:21 - Update parameters:
idhanghoa: 45 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen, Tím, Trắng, Vàng (type: string)
idThuocTinhHH: 30 (type: integer)
2025-04-12 05:59:21 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 45
    [1] => 7
    [2] => Đen, Tím, Trắng, Vàng
    [3] => 30
)

2025-04-12 05:59:21 - Success, rows affected: 1
2025-04-12 05:59:21 - Update parameters:
idhanghoa: 45 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen, Tím, Trắng, Vàng (type: string)
idThuocTinhHH: 30 (type: integer)
2025-04-12 05:59:21 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 45
    [1] => 7
    [2] => Đen, Tím, Trắng, Vàng
    [3] => 30
)

2025-04-12 05:59:21 - Success, rows affected: 0
2025-06-19 01:42:21 - Update parameters:
idhanghoa: 45 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen, Tím, Trắng, Vànggggg (type: string)
idThuocTinhHH: 30 (type: integer)
2025-06-19 01:42:21 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 45
    [1] => 7
    [2] => Đen, Tím, Trắng, Vànggggg
    [3] => 30
)

2025-06-19 01:42:21 - Success, rows affected: 1
2025-06-19 01:42:21 - Update parameters:
idhanghoa: 45 (type: integer)
idThuocTinh: 7 (type: integer)
tenThuocTinhHH: Đen, Tím, Trắng, Vànggggg (type: string)
idThuocTinhHH: 30 (type: integer)
2025-06-19 01:42:21 - SQL Query: UPDATE thuoctinhhh 
                SET idhanghoa = ?, idThuocTinh = ?, tenThuocTinhHH = ?
                WHERE idThuocTinhHH = ?
Data: Array
(
    [0] => 45
    [1] => 7
    [2] => Đen, Tím, Trắng, Vànggggg
    [3] => 30
)

2025-06-19 01:42:21 - Success, rows affected: 0
