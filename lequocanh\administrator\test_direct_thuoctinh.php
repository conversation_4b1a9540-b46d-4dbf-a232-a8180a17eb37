<?php
echo "<h1>🔍 TEST TRỰC TIẾP THUOCTINH ACT</h1>";

// Test include file
echo "<h2>1. Test include file:</h2>";
try {
    require_once 'elements_LQA/mod/thuoctinhCls.php';
    echo "✅ Include thuoctinhCls.php thành công<br>";
    
    $thuoctinh = new ThuocTinh();
    echo "✅ Tạo object ThuocTinh thành công<br>";
    
} catch (Exception $e) {
    echo "❌ Lỗi include: " . $e->getMessage() . "<br>";
}

// Test đường dẫn file
echo "<h2>2. Test đường dẫn file:</h2>";
$file_path = 'elements_LQA/mthuoctinh/thuoctinhAct.php';
if (file_exists($file_path)) {
    echo "✅ File $file_path tồn tại<br>";
    echo "📁 Đường dẫn đầy đủ: " . realpath($file_path) . "<br>";
} else {
    echo "❌ File $file_path không tồn tại<br>";
}

// Test quyền truy cập
echo "<h2>3. Test quyền truy cập:</h2>";
if (is_readable($file_path)) {
    echo "✅ File có thể đọc được<br>";
} else {
    echo "❌ File không thể đọc được<br>";
}

// Test syntax file
echo "<h2>4. Test syntax file:</h2>";
$output = [];
$return_var = 0;
exec("php -l $file_path 2>&1", $output, $return_var);
if ($return_var === 0) {
    echo "✅ Syntax file hợp lệ<br>";
} else {
    echo "❌ Lỗi syntax:<br>";
    foreach ($output as $line) {
        echo htmlspecialchars($line) . "<br>";
    }
}

// Test POST request simulation
echo "<h2>5. Test POST request simulation:</h2>";
$_POST['idThuocTinh'] = 7;
$_POST['tenThuocTinh'] = 'Test Direct';
$_POST['ghiChu'] = 'Test ghi chú direct';
$_GET['reqact'] = 'updatethuoctinh';

echo "Dữ liệu POST được set:<br>";
echo "- idThuocTinh: " . $_POST['idThuocTinh'] . "<br>";
echo "- tenThuocTinh: " . $_POST['tenThuocTinh'] . "<br>";
echo "- ghiChu: " . $_POST['ghiChu'] . "<br>";
echo "- reqact: " . $_GET['reqact'] . "<br>";

// Capture output
ob_start();
try {
    include $file_path;
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "<h3>Kết quả thực thi:</h3>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    // Try to decode as JSON
    $json_data = json_decode($output, true);
    if ($json_data !== null) {
        echo "<h3>JSON Response:</h3>";
        echo "<pre>" . print_r($json_data, true) . "</pre>";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Lỗi thực thi: " . $e->getMessage() . "<br>";
}

echo "<div style='margin: 20px 0;'>";
echo "<a href='index.php?req=thuoctinhview' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📋 Quay lại quản lý thuộc tính</a>";
echo "</div>";
?>
