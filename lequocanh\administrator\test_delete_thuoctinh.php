<?php
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/thuoctinhCls.php';

echo "<h1>🔍 TEST XÓA THUỘC TÍNH</h1>";

try {
    $thuoctinhObj = new ThuocTinh();
    
    // L<PERSON>y danh sách thuộc tính
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>1. DANH SÁCH THUỘC TÍNH:</h2>";
    
    $stmt = $conn->query("SELECT idThuocTinh, tenThuocTinh FROM thuoctinh LIMIT 5");
    $thuoctinhList = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($thuoctinhList)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Tên thuộc tính</th>";
        echo "<th style='padding: 8px;'>Kiểm tra liên kết</th>";
        echo "<th style='padding: 8px;'>Test xóa</th>";
        echo "</tr>";
        
        foreach ($thuoctinhList as $thuoctinh) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $thuoctinh['idThuocTinh'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($thuoctinh['tenThuocTinh']) . "</td>";
            
            // Kiểm tra dữ liệu liên quan
            $relatedData = $thuoctinhObj->checkRelatedData($thuoctinh['idThuocTinh']);
            echo "<td style='padding: 8px;'>";
            if (!empty($relatedData)) {
                echo "❌ Có liên kết:<br>";
                foreach ($relatedData as $table) {
                    echo "- " . $table['display_name'] . ": " . $table['count'] . "<br>";
                }
            } else {
                echo "✅ Không có liên kết";
            }
            echo "</td>";
            
            echo "<td style='padding: 8px;'>";
            echo "<a href='test_delete_thuoctinh.php?test_delete=" . $thuoctinh['idThuocTinh'] . "' style='background: #dc3545; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test xóa</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Không có thuộc tính nào</p>";
    }
    echo "</div>";
    
    // Test xóa nếu có tham số
    if (isset($_GET['test_delete'])) {
        $idThuocTinh = intval($_GET['test_delete']);
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h2>2. TEST XÓA THUỘC TÍNH ID: $idThuocTinh</h2>";
        
        // Lấy thông tin thuộc tính
        $thuoctinhInfo = $thuoctinhObj->thuoctinhGetById($idThuocTinh);
        if ($thuoctinhInfo) {
            echo "<p><strong>Tên thuộc tính:</strong> " . htmlspecialchars($thuoctinhInfo->tenThuocTinh) . "</p>";
            
            // Test method thuoctinhDelete
            $result = $thuoctinhObj->thuoctinhDelete($idThuocTinh);
            
            echo "<h3>Kết quả test:</h3>";
            echo "<pre>" . print_r($result, true) . "</pre>";
            
            if (is_array($result)) {
                if ($result['success']) {
                    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
                    echo "✅ " . $result['message'];
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
                    echo "❌ " . $result['message'];
                    
                    if (isset($result['related_tables'])) {
                        echo "<h4>Dữ liệu liên quan:</h4>";
                        echo "<ul>";
                        foreach ($result['related_tables'] as $table) {
                            echo "<li><strong>" . $table['display_name'] . ":</strong> " . $table['description'] . "</li>";
                        }
                        echo "</ul>";
                    }
                    
                    if (isset($result['suggested_action'])) {
                        echo "<h4>Gợi ý:</h4>";
                        echo "<p>" . $result['suggested_action'] . "</p>";
                    }
                    echo "</div>";
                }
            }
        } else {
            echo "<p>❌ Không tìm thấy thuộc tính với ID: $idThuocTinh</p>";
        }
        echo "</div>";
    }
    
    // Hiển thị thống kê
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>3. THỐNG KÊ DỮ LIỆU LIÊN QUAN:</h2>";
    
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM thuoctinhhh");
        $count = $stmt->fetchColumn();
        echo "<p><strong>Bảng thuoctinhhh:</strong> $count bản ghi</p>";
        
        // Hiển thị chi tiết thuộc tính nào đang được sử dụng
        $stmt = $conn->query("
            SELECT tt.idThuocTinh, tt.tenThuocTinh, COUNT(tthh.idThuocTinhHH) as so_luong_su_dung
            FROM thuoctinh tt 
            LEFT JOIN thuoctinhhh tthh ON tt.idThuocTinh = tthh.idThuocTinh 
            GROUP BY tt.idThuocTinh, tt.tenThuocTinh
            HAVING so_luong_su_dung > 0
            ORDER BY so_luong_su_dung DESC
        ");
        $usedProperties = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($usedProperties)) {
            echo "<h4>Thuộc tính đang được sử dụng:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #ffc107; color: black;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Tên thuộc tính</th>";
            echo "<th style='padding: 8px;'>Số lượng sử dụng</th>";
            echo "</tr>";
            
            foreach ($usedProperties as $prop) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $prop['idThuocTinh'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($prop['tenThuocTinh']) . "</td>";
                echo "<td style='padding: 8px;'>" . $prop['so_luong_su_dung'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p><strong>Bảng thuoctinhhh:</strong> Không tồn tại hoặc lỗi</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ Lỗi: " . $e->getMessage() . "</h3>";
    echo "</div>";
}

echo "<div style='margin: 20px 0;'>";
echo "<a href='index.php?req=thuoctinhview' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📋 Quay lại quản lý thuộc tính</a>";
echo "<a href='index.php?req=thuoctinhhhview' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔗 Quản lý thuộc tính hàng hóa</a>";
echo "</div>";
?>
