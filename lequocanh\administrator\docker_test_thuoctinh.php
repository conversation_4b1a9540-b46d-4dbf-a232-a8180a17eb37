<?php
/**
 * Docker Terminal Test Script for Thuộc Tính Update
 * Run: docker exec -it [container_name] php /var/www/html/administrator/docker_test_thuoctinh.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once __DIR__ . '/elements_LQA/mod/database.php';
require_once __DIR__ . '/elements_LQA/mod/thuoctinhCls.php';

class ThuocTinhTester {
    private $thuoctinhObj;
    private $db;
    private $testResults = [];
    
    public function __construct() {
        $this->thuoctinhObj = new ThuocTinh();
        $this->db = Database::getInstance();
        echo "🐳 DOCKER TEST - THUỘC TÍNH UPDATE\n";
        echo "================================\n\n";
    }
    
    public function runAllTests() {
        $this->testDatabaseConnection();
        $this->testThuocTinhClass();
        $this->testDataRetrieval();
        $this->testUpdateFunction();
        $this->testFileExistence();
        $this->testPermissions();
        $this->printSummary();
    }
    
    private function testDatabaseConnection() {
        echo "📊 Testing Database Connection...\n";
        try {
            $conn = $this->db->getConnection();
            if ($conn) {
                $this->addResult("Database Connection", true, "Connected successfully");
                
                // Test thuoctinh table
                $stmt = $conn->query("SHOW TABLES LIKE 'thuoctinh'");
                if ($stmt->rowCount() > 0) {
                    $this->addResult("Table 'thuoctinh' exists", true, "Table found");
                } else {
                    $this->addResult("Table 'thuoctinh' exists", false, "Table not found");
                }
                
                // Test table structure
                $stmt = $conn->query("DESCRIBE thuoctinh");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $expectedColumns = ['idThuocTinh', 'tenThuocTinh', 'ghiChu', 'hinhanh'];
                $missingColumns = array_diff($expectedColumns, $columns);
                
                if (empty($missingColumns)) {
                    $this->addResult("Table structure", true, "All required columns exist");
                } else {
                    $this->addResult("Table structure", false, "Missing columns: " . implode(', ', $missingColumns));
                }
                
            } else {
                $this->addResult("Database Connection", false, "Connection failed");
            }
        } catch (Exception $e) {
            $this->addResult("Database Connection", false, "Error: " . $e->getMessage());
        }
        echo "\n";
    }
    
    private function testThuocTinhClass() {
        echo "🏗️ Testing ThuocTinh Class...\n";
        
        if (class_exists('ThuocTinh')) {
            $this->addResult("ThuocTinh class exists", true, "Class loaded");
            
            // Test methods
            $methods = ['thuoctinhGetAll', 'thuoctinhGetById', 'thuoctinhUpdate', 'thuoctinhAdd', 'thuoctinhDelete'];
            foreach ($methods as $method) {
                if (method_exists($this->thuoctinhObj, $method)) {
                    $this->addResult("Method $method", true, "Method exists");
                } else {
                    $this->addResult("Method $method", false, "Method not found");
                }
            }
        } else {
            $this->addResult("ThuocTinh class exists", false, "Class not found");
        }
        echo "\n";
    }
    
    private function testDataRetrieval() {
        echo "📋 Testing Data Retrieval...\n";
        
        try {
            $data = $this->thuoctinhObj->thuoctinhGetAll();
            if ($data && count($data) > 0) {
                $this->addResult("Data retrieval", true, "Found " . count($data) . " records");
                
                // Test first record structure
                $firstRecord = $data[0];
                $requiredFields = ['idThuocTinh', 'tenThuocTinh', 'ghiChu'];
                $missingFields = [];
                
                foreach ($requiredFields as $field) {
                    if (!property_exists($firstRecord, $field)) {
                        $missingFields[] = $field;
                    }
                }
                
                if (empty($missingFields)) {
                    $this->addResult("Record structure", true, "All required fields present");
                    
                    // Display sample data
                    echo "   Sample record:\n";
                    echo "   - ID: " . $firstRecord->idThuocTinh . "\n";
                    echo "   - Tên: " . $firstRecord->tenThuocTinh . "\n";
                    echo "   - Ghi chú: " . $firstRecord->ghiChu . "\n";
                } else {
                    $this->addResult("Record structure", false, "Missing fields: " . implode(', ', $missingFields));
                }
            } else {
                $this->addResult("Data retrieval", false, "No data found or query failed");
            }
        } catch (Exception $e) {
            $this->addResult("Data retrieval", false, "Error: " . $e->getMessage());
        }
        echo "\n";
    }
    
    private function testUpdateFunction() {
        echo "🔄 Testing Update Function...\n";
        
        try {
            // Get first record for testing
            $data = $this->thuoctinhObj->thuoctinhGetAll();
            if ($data && count($data) > 0) {
                $testRecord = $data[0];
                $originalName = $testRecord->tenThuocTinh;
                $originalNote = $testRecord->ghiChu;
                $testId = $testRecord->idThuocTinh;
                
                echo "   Testing with ID: $testId\n";
                echo "   Original name: $originalName\n";
                echo "   Original note: $originalNote\n";
                
                // Test update with new values
                $newName = $originalName . " [TEST]";
                $newNote = $originalNote . " [UPDATED " . date('Y-m-d H:i:s') . "]";
                
                echo "   Attempting update...\n";
                $result = $this->thuoctinhObj->thuoctinhUpdate($newName, $newNote, '', $testId);
                
                if ($result) {
                    $this->addResult("Update function", true, "Update successful, rows affected: $result");
                    
                    // Verify update
                    $updatedRecord = $this->thuoctinhObj->thuoctinhGetById($testId);
                    if ($updatedRecord && $updatedRecord->tenThuocTinh === $newName) {
                        $this->addResult("Update verification", true, "Data updated correctly");
                        
                        // Restore original values
                        echo "   Restoring original values...\n";
                        $restoreResult = $this->thuoctinhObj->thuoctinhUpdate($originalName, $originalNote, '', $testId);
                        if ($restoreResult) {
                            $this->addResult("Data restoration", true, "Original data restored");
                        } else {
                            $this->addResult("Data restoration", false, "Failed to restore original data");
                        }
                    } else {
                        $this->addResult("Update verification", false, "Data not updated correctly");
                    }
                } else {
                    $this->addResult("Update function", false, "Update failed");
                }
            } else {
                $this->addResult("Update function", false, "No test data available");
            }
        } catch (Exception $e) {
            $this->addResult("Update function", false, "Error: " . $e->getMessage());
        }
        echo "\n";
    }
    
    private function testFileExistence() {
        echo "📁 Testing File Existence...\n";
        
        $files = [
            'elements_LQA/mthuoctinh/thuoctinhView.php',
            'elements_LQA/mthuoctinh/thuoctinhUpdate.php',
            'elements_LQA/mthuoctinh/thuoctinhAct.php',
            'elements_LQA/mod/thuoctinhCls.php',
            'elements_LQA/mod/database.php'
        ];
        
        foreach ($files as $file) {
            $fullPath = __DIR__ . '/' . $file;
            if (file_exists($fullPath)) {
                $size = filesize($fullPath);
                $this->addResult("File: $file", true, "Exists, size: " . number_format($size) . " bytes");
            } else {
                $this->addResult("File: $file", false, "File not found");
            }
        }
        echo "\n";
    }
    
    private function testPermissions() {
        echo "🔐 Testing File Permissions...\n";
        
        $files = [
            'elements_LQA/mthuoctinh/thuoctinhAct.php',
            'elements_LQA/mod/thuoctinhCls.php'
        ];
        
        foreach ($files as $file) {
            $fullPath = __DIR__ . '/' . $file;
            if (file_exists($fullPath)) {
                $readable = is_readable($fullPath);
                $writable = is_writable($fullPath);
                $perms = substr(sprintf('%o', fileperms($fullPath)), -4);
                
                $status = "Permissions: $perms";
                if ($readable) $status .= ", Readable";
                if ($writable) $status .= ", Writable";
                
                $this->addResult("Permissions: $file", $readable, $status);
            }
        }
        echo "\n";
    }
    
    private function addResult($test, $success, $message) {
        $this->testResults[] = [
            'test' => $test,
            'success' => $success,
            'message' => $message
        ];
        
        $icon = $success ? "✅" : "❌";
        echo "   $icon $test: $message\n";
    }
    
    private function printSummary() {
        echo "📊 TEST SUMMARY\n";
        echo "===============\n";
        
        $total = count($this->testResults);
        $passed = array_filter($this->testResults, function($result) {
            return $result['success'];
        });
        $passedCount = count($passed);
        $failedCount = $total - $passedCount;
        
        echo "Total tests: $total\n";
        echo "✅ Passed: $passedCount\n";
        echo "❌ Failed: $failedCount\n";
        echo "Success rate: " . round(($passedCount / $total) * 100, 1) . "%\n\n";
        
        if ($failedCount > 0) {
            echo "❌ FAILED TESTS:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "   - " . $result['test'] . ": " . $result['message'] . "\n";
                }
            }
            echo "\n";
        }
        
        // Overall status
        if ($failedCount === 0) {
            echo "🎉 ALL TESTS PASSED! Thuộc tính update should work correctly.\n";
        } else if ($failedCount <= 2) {
            echo "⚠️  MOSTLY WORKING with minor issues. Check failed tests above.\n";
        } else {
            echo "🚨 MULTIPLE ISSUES DETECTED. Please fix failed tests before using.\n";
        }
        
        echo "\n🐳 Docker test completed at " . date('Y-m-d H:i:s') . "\n";
    }
}

// Run tests
try {
    $tester = new ThuocTinhTester();
    $tester->runAllTests();
} catch (Exception $e) {
    echo "🚨 FATAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
