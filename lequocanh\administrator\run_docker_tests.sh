#!/bin/bash

# Docker Test Runner for Thuộc Tính Update
# Usage: ./run_docker_tests.sh [container_name]

echo "🐳 DOCKER TEST RUNNER - THUỘC TÍNH UPDATE"
echo "=========================================="
echo ""

# Get container name
CONTAINER_NAME=${1:-"php_container"}

# Check if container exists and is running
echo "🔍 Checking Docker container..."
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "❌ Container '$CONTAINER_NAME' is not running!"
    echo ""
    echo "Available containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    echo "Usage: $0 [container_name]"
    echo "Example: $0 my_php_container"
    exit 1
fi

echo "✅ Container '$CONTAINER_NAME' is running"
echo ""

# Function to run test and capture output
run_test() {
    local test_name="$1"
    local test_file="$2"
    local container="$3"
    
    echo "🧪 Running $test_name..."
    echo "----------------------------------------"
    
    # Run the test
    docker exec -it "$container" php "/var/www/html/administrator/$test_file"
    local exit_code=$?
    
    echo ""
    echo "Exit code: $exit_code"
    echo "----------------------------------------"
    echo ""
    
    return $exit_code
}

# Run all tests
echo "🚀 Starting test suite..."
echo ""

# Test 1: Basic functionality
run_test "Basic Functionality Test" "docker_test_thuoctinh.php" "$CONTAINER_NAME"
BASIC_TEST_RESULT=$?

# Test 2: AJAX endpoints
run_test "AJAX Endpoints Test" "docker_test_ajax.php" "$CONTAINER_NAME"
AJAX_TEST_RESULT=$?

# Test 3: File permissions and structure
echo "🧪 Running File Structure Test..."
echo "----------------------------------------"
docker exec -it "$CONTAINER_NAME" bash -c "
cd /var/www/html/administrator
echo '📁 Checking file structure...'
echo ''

# Check main files
files=(
    'elements_LQA/mthuoctinh/thuoctinhView.php'
    'elements_LQA/mthuoctinh/thuoctinhUpdate.php'
    'elements_LQA/mthuoctinh/thuoctinhAct.php'
    'elements_LQA/mod/thuoctinhCls.php'
    'elements_LQA/mod/database.php'
)

for file in \"\${files[@]}\"; do
    if [ -f \"\$file\" ]; then
        size=\$(stat -c%s \"\$file\")
        perms=\$(stat -c%a \"\$file\")
        echo \"✅ \$file - Size: \$size bytes, Permissions: \$perms\"
    else
        echo \"❌ \$file - NOT FOUND\"
    fi
done

echo ''
echo '🔐 Checking permissions...'
echo ''

# Check if files are readable/writable
for file in \"\${files[@]}\"; do
    if [ -f \"\$file\" ]; then
        if [ -r \"\$file\" ]; then
            echo \"✅ \$file is readable\"
        else
            echo \"❌ \$file is NOT readable\"
        fi
        
        if [ -w \"\$file\" ]; then
            echo \"✅ \$file is writable\"
        else
            echo \"⚠️  \$file is NOT writable\"
        fi
    fi
done

echo ''
echo '🌐 Checking web server access...'
echo ''

# Test if files are accessible via web server
if command -v curl >/dev/null 2>&1; then
    echo 'Testing HTTP access...'
    
    # Test thuoctinhUpdate.php (should require POST)
    response=\$(curl -s -o /dev/null -w '%{http_code}' -X POST -d 'idThuocTinh=1' http://localhost/administrator/elements_LQA/mthuoctinh/thuoctinhUpdate.php)
    if [ \"\$response\" = '200' ]; then
        echo '✅ thuoctinhUpdate.php is accessible via HTTP'
    else
        echo \"❌ thuoctinhUpdate.php HTTP test failed (code: \$response)\"
    fi
    
    # Test thuoctinhAct.php (should require GET reqact)
    response=\$(curl -s -o /dev/null -w '%{http_code}' 'http://localhost/administrator/elements_LQA/mthuoctinh/thuoctinhAct.php?reqact=updatethuoctinh')
    if [ \"\$response\" = '200' ]; then
        echo '✅ thuoctinhAct.php is accessible via HTTP'
    else
        echo \"❌ thuoctinhAct.php HTTP test failed (code: \$response)\"
    fi
else
    echo '⚠️  curl not available, skipping HTTP tests'
fi
"
FILE_TEST_RESULT=$?

echo "Exit code: $FILE_TEST_RESULT"
echo "----------------------------------------"
echo ""

# Test 4: Database connectivity
echo "🧪 Running Database Connectivity Test..."
echo "----------------------------------------"
docker exec -it "$CONTAINER_NAME" bash -c "
cd /var/www/html/administrator
php -r \"
try {
    require_once 'elements_LQA/mod/database.php';
    \$db = Database::getInstance();
    \$conn = \$db->getConnection();
    
    if (\$conn) {
        echo '✅ Database connection successful\n';
        
        // Test thuoctinh table
        \$stmt = \$conn->query('SELECT COUNT(*) as count FROM thuoctinh');
        \$result = \$stmt->fetch(PDO::FETCH_ASSOC);
        echo '✅ thuoctinh table accessible, records: ' . \$result['count'] . '\n';
        
        // Test a simple select
        \$stmt = \$conn->query('SELECT idThuocTinh, tenThuocTinh FROM thuoctinh LIMIT 1');
        \$record = \$stmt->fetch(PDO::FETCH_ASSOC);
        if (\$record) {
            echo '✅ Sample record: ID=' . \$record['idThuocTinh'] . ', Name=' . \$record['tenThuocTinh'] . '\n';
        } else {
            echo '⚠️  No records found in thuoctinh table\n';
        }
    } else {
        echo '❌ Database connection failed\n';
        exit(1);
    }
} catch (Exception \$e) {
    echo '❌ Database error: ' . \$e->getMessage() . '\n';
    exit(1);
}
\"
"
DB_TEST_RESULT=$?

echo "Exit code: $DB_TEST_RESULT"
echo "----------------------------------------"
echo ""

# Summary
echo "📊 TEST SUITE SUMMARY"
echo "====================="
echo ""

total_tests=4
passed_tests=0

echo "Test Results:"
if [ $BASIC_TEST_RESULT -eq 0 ]; then
    echo "✅ Basic Functionality Test: PASSED"
    ((passed_tests++))
else
    echo "❌ Basic Functionality Test: FAILED"
fi

if [ $AJAX_TEST_RESULT -eq 0 ]; then
    echo "✅ AJAX Endpoints Test: PASSED"
    ((passed_tests++))
else
    echo "❌ AJAX Endpoints Test: FAILED"
fi

if [ $FILE_TEST_RESULT -eq 0 ]; then
    echo "✅ File Structure Test: PASSED"
    ((passed_tests++))
else
    echo "❌ File Structure Test: FAILED"
fi

if [ $DB_TEST_RESULT -eq 0 ]; then
    echo "✅ Database Connectivity Test: PASSED"
    ((passed_tests++))
else
    echo "❌ Database Connectivity Test: FAILED"
fi

echo ""
echo "Summary: $passed_tests/$total_tests tests passed"

# Calculate percentage
percentage=$((passed_tests * 100 / total_tests))
echo "Success rate: $percentage%"

echo ""
if [ $passed_tests -eq $total_tests ]; then
    echo "🎉 ALL TESTS PASSED! Thuộc tính update functionality is ready to use."
    exit 0
elif [ $passed_tests -ge 3 ]; then
    echo "⚠️  MOSTLY WORKING with minor issues. Check failed tests above."
    exit 1
else
    echo "🚨 MULTIPLE ISSUES DETECTED. Please fix failed tests before using."
    exit 2
fi
