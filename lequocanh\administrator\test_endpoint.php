<?php
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/thuoctinhCls.php';

echo "<h1>🔧 TEST ENDPOINT thuoctinhAct.php</h1>";

// Test if we can access the endpoint
$endpoint_path = __DIR__ . '/elements_LQA/mthuoctinh/thuoctinhAct.php';
echo "<p><strong>Endpoint path:</strong> " . $endpoint_path . "</p>";
echo "<p><strong>File exists:</strong> " . (file_exists($endpoint_path) ? 'YES' : 'NO') . "</p>";

if (file_exists($endpoint_path)) {
    echo "<p><strong>File readable:</strong> " . (is_readable($endpoint_path) ? 'YES' : 'NO') . "</p>";
}

// Get a test attribute
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    $stmt = $conn->query("SELECT * FROM thuoctinh LIMIT 1");
    $testItem = $stmt->fetch(PDO::FETCH_OBJ);
    
    if ($testItem) {
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h2>Test Data:</h2>";
        echo "<ul>";
        echo "<li>ID: " . $testItem->idThuocTinh . "</li>";
        echo "<li>Name: " . htmlspecialchars($testItem->tenThuocTinh) . "</li>";
        echo "<li>Note: " . htmlspecialchars($testItem->ghiChu) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Test direct POST to endpoint
        if (isset($_POST['test_direct'])) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h2>Direct POST Test Result:</h2>";
            
            // Simulate POST data
            $_POST['reqact'] = 'updatethuoctinh';
            $_POST['idThuocTinh'] = $testItem->idThuocTinh;
            $_POST['tenThuocTinh'] = $testItem->tenThuocTinh . ' (Direct Test)';
            $_POST['ghiChu'] = $testItem->ghiChu . ' (Direct)';
            $_POST['hinhanh'] = $testItem->hinhanh;
            $_GET['reqact'] = 'updatethuoctinh';
            
            // Capture output
            ob_start();
            include $endpoint_path;
            $output = ob_get_clean();
            
            echo "<p><strong>Output:</strong></p>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            echo "</div>";
        }
        
    } else {
        echo "<p>No test data available</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h2>❌ Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<div style="margin: 20px 0;">
    <h2>Test Actions:</h2>
    <form method="post">
        <button type="submit" name="test_direct" value="1">Test Direct POST</button>
    </form>
</div>

<div style="margin: 20px 0;">
    <h2>AJAX Test:</h2>
    <button onclick="testAjaxEndpoint()">Test AJAX to Endpoint</button>
    <div id="ajax-result" style="margin-top: 10px;"></div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testAjaxEndpoint() {
    $('#ajax-result').html('<div style="color: blue;">Testing AJAX endpoint...</div>');
    
    var formData = new FormData();
    formData.append('reqact', 'updatethuoctinh');
    formData.append('idThuocTinh', '<?php echo $testItem ? $testItem->idThuocTinh : 1; ?>');
    formData.append('tenThuocTinh', 'AJAX Test <?php echo date("H:i:s"); ?>');
    formData.append('ghiChu', 'AJAX test note');
    
    console.log('Sending AJAX request to: ./elements_LQA/mthuoctinh/thuoctinhAct.php');
    
    $.ajax({
        url: './elements_LQA/mthuoctinh/thuoctinhAct.php',
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        dataType: 'json',
        success: function(response) {
            console.log('AJAX Success:', response);
            
            if (response && response.success) {
                $('#ajax-result').html('<div style="color: green;">✅ AJAX Success: ' + response.message + '</div>');
            } else {
                $('#ajax-result').html('<div style="color: red;">❌ AJAX Failed: ' + (response ? response.message : 'No response') + '</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            console.error('Status:', xhr.status);
            console.error('Response:', xhr.responseText);
            
            $('#ajax-result').html('<div style="color: red;">❌ AJAX Error: ' + error + ' (Status: ' + xhr.status + ')<br><strong>Response:</strong><br><pre>' + xhr.responseText.substring(0, 500) + '</pre></div>');
        }
    });
}
</script>

<style>
pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    max-height: 300px;
}
button {
    padding: 10px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin: 5px;
}
button:hover {
    background-color: #0056b3;
}
</style>
