body {
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
}

.container {
  width: 100%;
  margin: 0 auto;
  display: flex;
  padding: 20px;
}

.left-column {
  width: 250px;
  min-width: 250px;
  background-color: #f8f9fa;
  padding: 20px;
  margin-right: 20px;
}

.right-column {
  flex: 1;
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.right-column + .right-column {
  margin-left: 20px;
}
.row {
  display: flex;
  flex-wrap: nowrap;
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
  
  .left-column {
    width: 100%;
    margin-right: 0;
    margin-bottom: 20px;
  }
}

/* Style cho menu trái */
.left-menu {
    background-color: #34495e;
    color: #ecf0f1;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.left-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.left-menu a {
    color: #ecf0f1;
    text-decoration: none;
    display: block;
    padding: 12px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    margin-bottom: 5px;
    position: relative;
    overflow: hidden;
}

.left-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

/* Style cho menu item active */
.left-menu a.active {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.left-menu a.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: #e74c3c;
}

.left-menu i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
}

/* Animation cho active item */
.left-menu a.active i {
    animation: bounce 0.5s ease;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}
