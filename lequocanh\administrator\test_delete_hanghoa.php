<?php
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/hanghoaCls.php';

echo "<h1>🔍 TEST XÓA HÀNG HÓA</h1>";

try {
    $hanghoaObj = new hanghoa();
    
    // Lấy danh sách hàng hóa
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>1. DANH SÁCH HÀNG HÓA:</h2>";
    
    $stmt = $conn->query("SELECT idhanghoa, tenhanghoa FROM hanghoa LIMIT 5");
    $hanghoaList = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($hanghoaList)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Tên hàng hóa</th>";
        echo "<th style='padding: 8px;'>Kiểm tra liên kết</th>";
        echo "<th style='padding: 8px;'>Test xóa</th>";
        echo "</tr>";
        
        foreach ($hanghoaList as $hanghoa) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $hanghoa['idhanghoa'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($hanghoa['tenhanghoa']) . "</td>";
            
            // Kiểm tra dữ liệu liên quan
            $relatedData = $hanghoaObj->checkRelatedData($hanghoa['idhanghoa']);
            echo "<td style='padding: 8px;'>";
            if (!empty($relatedData)) {
                echo "❌ Có liên kết:<br>";
                foreach ($relatedData as $table) {
                    echo "- " . $table['display_name'] . ": " . $table['count'] . "<br>";
                }
            } else {
                echo "✅ Không có liên kết";
            }
            echo "</td>";
            
            echo "<td style='padding: 8px;'>";
            echo "<a href='test_delete_hanghoa.php?test_delete=" . $hanghoa['idhanghoa'] . "' style='background: #dc3545; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test xóa</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Không có hàng hóa nào</p>";
    }
    echo "</div>";
    
    // Test xóa nếu có tham số
    if (isset($_GET['test_delete'])) {
        $idhanghoa = intval($_GET['test_delete']);
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h2>2. TEST XÓA HÀNG HÓA ID: $idhanghoa</h2>";
        
        // Lấy thông tin hàng hóa
        $hanghoaInfo = $hanghoaObj->HanghoaGetbyId($idhanghoa);
        if ($hanghoaInfo) {
            echo "<p><strong>Tên hàng hóa:</strong> " . htmlspecialchars($hanghoaInfo->tenhanghoa) . "</p>";
            
            // Test method HanghoaDelete
            $result = $hanghoaObj->HanghoaDelete($idhanghoa);
            
            echo "<h3>Kết quả test:</h3>";
            echo "<pre>" . print_r($result, true) . "</pre>";
            
            if (is_array($result)) {
                if ($result['success']) {
                    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
                    echo "✅ " . $result['message'];
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
                    echo "❌ " . $result['message'];
                    
                    if (isset($result['related_tables'])) {
                        echo "<h4>Dữ liệu liên quan:</h4>";
                        echo "<ul>";
                        foreach ($result['related_tables'] as $table) {
                            echo "<li><strong>" . $table['display_name'] . ":</strong> " . $table['description'] . "</li>";
                        }
                        echo "</ul>";
                    }
                    
                    if (isset($result['suggested_action'])) {
                        echo "<h4>Gợi ý:</h4>";
                        echo "<p>" . $result['suggested_action'] . "</p>";
                    }
                    echo "</div>";
                }
            }
        } else {
            echo "<p>❌ Không tìm thấy hàng hóa với ID: $idhanghoa</p>";
        }
        echo "</div>";
    }
    
    // Hiển thị thống kê
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>3. THỐNG KÊ DỮ LIỆU LIÊN QUAN:</h2>";
    
    $tables = ['tonkho', 'chitiethoadon', 'chitietphieunhap', 'thuoctinhhh', 'dongia'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p><strong>Bảng $table:</strong> $count bản ghi</p>";
        } catch (Exception $e) {
            echo "<p><strong>Bảng $table:</strong> Không tồn tại hoặc lỗi</p>";
        }
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ Lỗi: " . $e->getMessage() . "</h3>";
    echo "</div>";
}

echo "<div style='margin: 20px 0;'>";
echo "<a href='index.php?req=hanghoaview' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📋 Quay lại quản lý hàng hóa</a>";
echo "</div>";
?>
