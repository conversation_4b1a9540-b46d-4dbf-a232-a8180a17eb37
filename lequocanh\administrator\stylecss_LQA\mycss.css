/* <PERSON><PERSON><PERSON><PERSON> quyết vấn đ<PERSON> hiển thị hình <PERSON>nh */
img[src$=".png"] {
  max-width: 40px !important;
  max-height: 40px !important;
  width: auto !important;
  height: auto !important;
}

.iconimg,
img[src*="Delete.png"],
img[src*="Update.png"],
img[src*="Wait.png"],
img[src*="Success.png"],
img[src*="Fail.png"],
img[src*="Lock.png"],
img[src*="Unlock.png"] {
  width: 24px !important;
  height: 24px !important;
  object-fit: contain !important;
}

/* <PERSON><PERSON><PERSON><PERSON> l<PERSON> cho hình <PERSON>nh sản phẩm */
.product-image,
img.product-image,
img[class*="product"] {
  max-width: 100px !important;
  max-height: 100px !important;
}

/* CSS gốc */
#top_div {
  min-height: 150px;
  width: 100%;
  border: solid 1px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  box-shadow: 5px 5px lightgray;
  margin: 5px;
  padding: 5px;
}
#left_div {
  min-height: 500px;
  width: 15%;
  float: left;
  height: auto;
  border: solid 1px #ccc;
  border-radius: 10px;
  box-shadow: 5px 5px rgba(0, 0, 0, 0.1);
  margin: 10px;
  padding: 10px;
}
#center_div {
  min-height: 500px;
  width: 65%;
  float: left;
  border: solid 1px #ccc;
  border-radius: 10px;
  box-shadow: 5px 5px rgba(0, 0, 0, 0.1);
  margin: 10px;
  padding: 10px;
}
/* #right_div {
  min-height: 500px;
  width: 20%;
  float: left;
  border: solid 1px #ccc;
  border-radius: 10px;
  box-shadow: 5px 5px rgba(0, 0, 0, 0.1);
  margin: 10px;
  padding: 10px;
} */
#bottom_div {
  min-height: 100px;
  width: 100%;
  clear: both;
  border: solid 1px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  box-shadow: 5px 5px lightgray;
  margin: 10px;
}

#signoutbutton {
  position: fixed;
  z-index: 3;
  top: 50px;
  right: 10px;
  opacity: 0.75;
}
.iconbutton {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}
.iconbutton:hover {
  transform: scale(1.1);
}

/* Định nghĩa toàn cục cho biểu tượng nhỏ */
.iconimg {
  width: 24px;
  height: 24px;
  margin: 0 5px;
  cursor: pointer;
}

.iconimg:hover {
  transform: scale(1.1);
  transition: transform 0.2s;
}

.cateOrder {
  font-weight: bold;
  color: #3b14bb;
}

.itemOrder {
  font-style: italic;
  color: #2b0f8f;
}

ul {
  list-style: none;
  margin: 0;
  padding-left: 15px;
  color: #001aff;
}

a {
  text-decoration: none;
  color: #7700ff;
}

a:hover {
  color: #4c00ff;
}

.isconing {
  height: 20px;
}

#signoutbutton {
  position: fixed;
  z-index: 3;
  top: 50px;
  right: 10px;
  opacity: 0.75;
}
.iconbutton {
  height: 30px;
  opacity: 1;
}
.iconbutton:hover {
  height: 40px;
  opacity: 1;
}

#w_update,
#w_update_th,
#w_update_nv,
#w_update_tt,
#w_update_tthh,
#w_update_hh,
#w_update_dvt {
  display: none;
  position: fixed;
  z-index: 1000;
  background: white;
  padding: 25px;
  border: 1px solid #ccc;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 8px;
  min-width: 500px;
  min-height: 200px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

#w_close_btn,
#w_close_btn_th,
#w_close_btn_nv,
#w_close_btn_tt,
#w_close_btn_tthh,
#w_close_btn_hh,
#w_close_btn_dvt {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  cursor: pointer;
  font-size: 16px;
  z-index: 1001;
}

#w_update_form_dvt {
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
}

#w_update_form_hh,
#w_update_form,
#w_update_form_dg,
#w_update_form_th,
#w_update_form_nv,
#w_update_form_dvt,
#w_update_form_tt,
#w_update_form_tthh {
  padding: 10px;
  max-width: 500px;
}

.content-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

.content-table th,
.content-table td {
  padding: 12px;
  text-align: left;
  border: 1px solid #ddd;
}

.content-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.content-table tr:hover {
  background-color: #f9f9f9;
}

.content-table td img.iconimg {
  display: inline-block;
  vertical-align: middle;
}

.content-table td[align="center"] {
  text-align: center;
}

.container {
  max-width: 600px;
  margin-top: 50px;
  padding: 20px;
  background: rgb(247, 202, 0);
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgb(247, 202, 0);
}

.title {
  font-size: 2.5em;
  margin-bottom: 20px;
  color: #333333;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
input[type="date"] {
  width: 100%;
  padding: 8px 15px;
  margin-bottom: 10px;
  border: 1px solid rgb(247, 202, 0);
  border-radius: 20px;
  background-color: #ffffff;
}

button {
  background: #2ecc71;
  border: none;
  color: #333333;
}

button:hover {
  background: #27ae60;
}

.footer {
  color: #134e5e;
}

/* Cập nhật style cho signup link */
.signup-link {
  text-align: center;
  margin-top: 24px;
  font-size: 14px;
  color: #6b7280;
}

.signup-link a {
  color: rgb(110, 23, 154);
  font-weight: 500;
  text-decoration: none;
}

.signup-link a:hover {
  color: rgb(108, 15, 221);
  text-decoration: underline;
}

/* Thay đổi màu sắc cho các mục trong menu */
.left-menu {
  background: linear-gradient(145deg, #2c3e50, #34495e);
  color: #ecf0f1;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.left-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.left-menu a {
  color: #ecf0f1;
  text-decoration: none;
  display: block;
  padding: 12px 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin: 5px 0;
}

.left-menu a:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
  color: #3498db;
}

/* Style cho menu item active */
.left-menu a.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.left-menu a.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #e74c3c;
}

/* Hiệu ứng hover cho active item */
.left-menu a.active:hover {
  background: linear-gradient(135deg, #2980b9, #3498db);
  transform: translateX(5px);
}

.left-menu i {
  width: 24px;
  margin-right: 10px;
  text-align: center;
  transition: all 0.3s ease;
}

/* Animation cho active item */
.left-menu a.active i {
  animation: bounce 0.5s ease;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Style cho menu header */
.left-menu > div > a {
  font-weight: bold;
  font-size: 1.1em;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Style cho submenu items */
.left-menu ul li a {
  font-size: 0.95em;
  padding-left: 20px;
}

/* Hiệu ứng ripple khi click */
.left-menu a::after {
  content: "";
  position: absolute;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.5s;
}

.left-menu a:active::after {
  transform: scale(2);
  opacity: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .left-menu {
    margin: 10px;
    padding: 15px;
  }

  .left-menu a {
    padding: 10px;
  }
}

/* CSS cho tên thuộc tính hàng hóa */
.tenthuoctinhhh {
  white-space: nowrap; /* Không cho xuong dòng */
  overflow: hidden; /* Ẩn phần nội dung tràn */
  text-overflow: ellipsis; /* Hiển thị dấu ... khi nội dung quá dài */
  max-width: 550px; /* Đặt độ rộng tối đa cho cột */
}

/* Style cho bảng loại hàng */
.title_loaihang {
  font-size: 1.5em;
  font-weight: bold;
  color: #2c3e50;
  margin: 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #3498db;
}

.content_loaihang table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  overflow: hidden;
}

.content_loaihang th {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 15px;
  text-align: left;
  font-weight: 500;
}

.content_loaihang td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
}

.content_loaihang tr:hover {
  background-color: #f5f8fa;
}

.content_loaihang img.iconbutton {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  transition: transform 0.2s;
}

.content_loaihang img.iconbutton:hover {
  transform: scale(1.1);
}

/* Style cho form thêm loại hàng */
#formaddloaihang {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

#formaddloaihang table {
  width: 100%;
}

#formaddloaihang td {
  padding: 10px;
}

#formaddloaihang input[type="text"],
#formaddloaihang input[type="file"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: border-color 0.3s;
}

#formaddloaihang input[type="text"]:focus {
  border-color: #3498db;
  outline: none;
}

#formaddloaihang input[type="submit"],
#formaddloaihang input[type="reset"] {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

#formaddloaihang input[type="submit"] {
  background: #2ecc71;
  color: white;
}

#formaddloaihang input[type="reset"] {
  background: #e74c3c;
  color: white;
  margin-left: 10px;
}

#formaddloaihang input[type="submit"]:hover {
  background: #27ae60;
}

#formaddloaihang input[type="reset"]:hover {
  background: #c0392b;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content_loaihang table {
    display: block;
    overflow-x: auto;
  }

  #formaddloaihang td {
    display: block;
    width: 100%;
  }
}

/* Style cho navigation bar */
.nav-menu {
  position: relative;
  background: linear-gradient(135deg, #1a2a6c, #2a4858);
  padding: 10px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 10px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  overflow: hidden;
}

.menu-items {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
  overflow-x: hidden;
  max-width: calc(100% - 120px); /* Để lại không gian cho nút dropdown */
}

.menu-items a {
  color: white;
  text-decoration: none;
  padding: 8px 15px;
  border-radius: 20px;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: fit-content;
}

.dropdown-btn {
  display: none;
  background: #e74c3c;
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  white-space: nowrap;
  align-items: center;
  gap: 5px;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  background: linear-gradient(135deg, #1a2a6c, #2a4858);
  min-width: 200px;
  max-height: 400px;
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  padding: 10px 0;
}

/* Auto-hide overflowing items */
@media (max-width: 1200px),
  (min-width: 1201px) and (.menu-items: :-webkit-scrollbar-track) {
  .menu-items {
    display: none;
  }

  .dropdown-btn {
    display: flex;
  }
}

/* Show dropdown when menu is active */
.nav-menu.show .dropdown-content {
  display: block;
}

/* Scrollbar styling for dropdown */
.dropdown-content::-webkit-scrollbar {
  width: 6px;
}

.dropdown-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

/* Responsive design */
@media (max-width: 768px) {
  .nav-menu {
    justify-content: center;
    padding: 5px;
  }

  .nav-menu a {
    padding: 6px 12px;
    font-size: 0.8em;
  }
}

/* Style cho menu dropdown */
.nav-menu {
  position: relative;
  overflow: hidden;
  height: 50px; /* Chiều cao cố định cho menu */
}

.nav-menu .menu-items {
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-menu .dropdown-btn {
  display: none;
  background: #e74c3c;
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  align-items: center;
  gap: 5px;
  margin-left: auto;
}

.nav-menu .dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  background: linear-gradient(135deg, #1a2a6c, #2a4858);
  min-width: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  padding: 10px 0;
}

.nav-menu .dropdown-content a {
  display: block;
  padding: 8px 20px;
  border-radius: 0;
}

.nav-menu .dropdown-content a:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

/* Responsive */
@media (max-width: 1200px) {
  .nav-menu .menu-items {
    display: none;
  }

  .nav-menu .dropdown-btn {
    display: flex;
  }

  .nav-menu.show .dropdown-content {
    display: block;
  }
}

/* Style cho navigation menu */
.nav-menu {
  position: relative;
  background: linear-gradient(135deg, #1a2a6c, #2a4858);
  padding: 10px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 10px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.menu-items {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
}

.menu-items a {
  color: white;
  text-decoration: none;
  padding: 8px 15px;
  border-radius: 20px;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dropdown-btn {
  background: #e74c3c;
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
  transition: all 0.3s ease;
}

.dropdown-btn:hover {
  background: #c0392b;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  background: linear-gradient(135deg, #1a2a6c, #2a4858);
  min-width: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  padding: 10px 0;
  margin-top: 5px;
}

.nav-menu.show .dropdown-content {
  display: block;
}

.dropdown-content a {
  color: white;
  text-decoration: none;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.dropdown-content a:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

/* Responsive styles */
@media (max-width: 1200px) {
  .menu-items {
    display: none;
  }

  .dropdown-btn {
    margin-left: auto;
  }

  .dropdown-content {
    width: 100%;
    left: 0;
  }
}

/* Style chung cho tất cả các form quản lý */
.admin-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.admin-form table {
  width: 100%;
}

.admin-form td {
  padding: 10px;
}

.admin-form input[type="text"],
.admin-form input[type="file"],
.admin-form input[type="number"],
.admin-form input[type="date"],
.admin-form select,
.admin-form textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: border-color 0.3s;
}

.admin-form input[type="text"]:focus,
.admin-form select:focus,
.admin-form textarea:focus {
  border-color: #3498db;
  outline: none;
}

.admin-form input[type="submit"],
.admin-form input[type="reset"] {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.admin-form input[type="submit"] {
  background: #2ecc71;
  color: white;
}

.admin-form input[type="reset"] {
  background: #e74c3c;
  color: white;
  margin-left: 10px;
}

.admin-form input[type="submit"]:hover {
  background: #27ae60;
}

.admin-form input[type="reset"]:hover {
  background: #c0392b;
}

/* Style cho bảng hiển thị dữ liệu */
.image-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.image-table th,
.image-table td {
  padding: 15px;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
  text-align: left;
}

.image-table th {
  background: linear-gradient(135deg, #1a2a6c, #2a4858);
  color: white;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.image-table tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

.image-table tbody tr:hover {
  background-color: #f0f7ff;
  transition: background-color 0.2s ease;
}

.image-table th:nth-child(1),
.image-table td:nth-child(1) {
  width: 50px;
  text-align: center;
}

.image-table th:nth-child(2),
.image-table td:nth-child(2) {
  width: 120px;
  text-align: center;
}

.preview-image {
  width: 100px;
  height: 100px;
  object-fit: contain;
  border-radius: 6px;
  border: 1px solid #ddd;
  padding: 4px;
  background: #fff;
  transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.preview-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: #3498db;
}

.badge {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  display: inline-block;
  white-space: nowrap;
  font-weight: 500;
}

.bg-success {
  background-color: #28a745;
  color: white;
}

.bg-secondary {
  background-color: #6c757d;
  color: white;
}

.delete-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: #dc3545;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.delete-btn:hover {
  background-color: #c82333;
  transform: translateY(-2px);
}

.delete-btn:disabled {
  background-color: #6c757d;
  opacity: 0.65;
  cursor: not-allowed;
}

/* Style cho checkbox */
.image-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* Style cho hình ảnh trong bảng hàng hóa */
.content-table .iconbutton {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid #ddd;
  padding: 2px;
  background: #fff;
  transition: transform 0.2s ease;
}

.content-table .iconbutton:hover {
  transform: scale(1.1);
}

/* Style cho cột hình ảnh */
.content-table td:nth-child(5) {
  width: 100px;
  text-align: center;
  vertical-align: middle;
  padding: 8px;
}

.upload-form {
  background: #fff;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
  border: 1px solid #eee;
}

.upload-form h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.upload-form .input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.upload-form input[type="file"] {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  flex: 1;
  background-color: #f9f9f9;
}

.upload-form button {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.upload-form button:hover {
  background: #0056b3;
  transform: translateY(-2px);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

#delete-selected {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: none;
  transition: background-color 0.2s;
}

#delete-selected:hover {
  background-color: #c82333;
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.form-check-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Styles for duplicate image handling - improved */
.duplicate-images-container {
  margin-top: 25px;
}

.duplicate-image-item {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
  padding: 25px;
  margin-bottom: 30px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e6e6e6;
}

.duplicate-image-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.duplicate-image-item h5 {
  color: #2c3e50;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
  font-weight: 600;
  font-size: 18px;
}

.image-comparison {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  margin-bottom: 25px;
  align-items: stretch;
}

.existing-image,
.new-image {
  flex: 1;
  min-width: 280px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.existing-image:hover,
.new-image:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.existing-image h6,
.new-image h6 {
  margin-bottom: 15px;
  color: #495057;
  font-weight: 600;
  font-size: 16px;
  position: relative;
  display: inline-block;
  padding-bottom: 8px;
}

.existing-image h6:after,
.new-image h6:after {
  content: "";
  position: absolute;
  width: 50px;
  height: 3px;
  background-color: #007bff;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.image-wrapper {
  position: relative;
  min-height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-bottom: 15px;
  padding: 15px;
  transition: all 0.3s ease;
}

.existing-image img,
.new-image img {
  max-width: 100%;
  max-height: 250px;
  object-fit: contain;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  background-color: #fff;
  padding: 8px;
  margin-bottom: 0;
  transition: transform 0.3s ease;
}

.existing-image img:hover,
.new-image img:hover {
  transform: scale(1.03);
}

.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
  background-color: #f8f9fa;
  border: 1px dashed #ced4da;
  border-radius: 5px;
  color: #6c757d;
  font-style: italic;
}

.existing-image p,
.new-image p {
  margin: 10px 0 0;
  color: #6c757d;
  font-size: 0.9em;
  font-weight: 500;
}

.image-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.image-actions button {
  padding: 10px 25px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 15px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.image-actions button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.image-actions button:active {
  transform: translateY(1px);
}

.image-actions .use-new-image {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.image-actions .use-new-image:hover {
  background-color: #0069d9;
}

.image-actions .use-existing-image {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.image-actions .use-existing-image:hover {
  background-color: #5a6268;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .image-comparison {
    flex-direction: column;
  }

  .existing-image,
  .new-image {
    width: 100%;
  }
}

/* Alert với icon */
.alert-with-icon {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
}

.alert-icon {
  font-size: 24px;
  color: #856404;
}

.alert-content {
  flex: 1;
}

.alert-heading {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #856404;
}

.product-name {
  font-weight: 700;
  color: #495057;
}

/* Overlay xử lý */
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: 10px;
  backdrop-filter: blur(3px);
}

.duplicate-image-item {
  position: relative;
}

.duplicate-image-item.processing .processing-overlay {
  display: flex;
}

.duplicate-image-item.processed {
  border-left: 4px solid #28a745;
  opacity: 0.8;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 123, 255, 0.2);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.spinner-container p {
  color: #007bff;
  font-weight: 500;
  font-size: 16px;
}

.result-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 8px 15px;
  border-radius: 30px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

.result-badge.success {
  background-color: #28a745;
  color: white;
}

.result-badge.info {
  background-color: #17a2b8;
  color: white;
}

.result-badge i {
  font-size: 14px;
}

.image-error-message {
  margin-top: 10px;
  padding: 5px;
  background-color: #fff3f3;
  border-radius: 4px;
  border-left: 3px solid #dc3545;
}

.show-debug {
  position: absolute;
  bottom: 5px;
  right: 5px;
  font-size: 0.8rem;
  padding: 3px 8px;
  opacity: 0.7;
}

.show-debug:hover {
  opacity: 1;
}

.debug-info {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  color: #00ff00;
  font-family: monospace;
  padding: 10px;
  overflow: auto;
  z-index: 100;
  border-radius: 6px;
}

.debug-info pre {
  white-space: pre-wrap;
  font-size: 0.8rem;
  margin: 0;
}

.content_loaihang img,
.content-table img {
  max-width: 40px;
  max-height: 40px;
  width: auto;
  height: auto;
}

/* Đảm bảo biểu tượng xóa và cập nhật luôn nhỏ */
img[src*="Delete.png"],
img[src*="Update.png"],
img[src*="delete.png"],
img[src*="update.png"] {
  width: 24px !important;
  height: 24px !important;
  object-fit: contain;
}

/* Fix cho biểu tượng X quá to */
img {
  max-width: 40px !important;
  max-height: 40px !important;
}

.content-table td img,
td img[onclick],
img[class="iconimg"] {
  width: 24px !important;
  height: 24px !important;
  object-fit: contain !important;
}

/* Giữ lại hình ảnh sản phẩm có kích thước lớn hơn */
img.product-image {
  max-width: 100px !important;
  max-height: 100px !important;
}

.update-form {
  padding: 20px;
  background: white;
  border-radius: 8px;
  max-width: 100%;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.btn-update,
.btn-reset {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-update {
  background-color: #007bff;
  color: white;
}

.btn-reset {
  background-color: #6c757d;
  color: white;
}

.btn-update:hover {
  background-color: #0056b3;
}

.btn-reset:hover {
  background-color: #5a6268;
}

/* Modal window styles */
.modal-window {
  display: none;
  position: fixed;
  z-index: 1000;
  background: white;
  padding: 20px;
  border: 1px solid #ccc;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 5px;
  min-width: 500px;
  min-height: 200px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-weight: bold;
  cursor: pointer;
}

.close-btn:hover {
  background: #d32f2f;
}

/* Override class for modal visibility issues - modified to be less aggressive */
.visible-modal {
  /* display: block !important; */ /* Commented out to prevent unwanted auto-display */
  visibility: visible;
  opacity: 1;
  z-index: 9999;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border: 2px solid #3498db;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

#w_update_th {
  display: none;
  position: fixed;
  z-index: 1000;
  background: white;
  padding: 25px;
  border: 1px solid #ccc;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 8px;
  min-width: 500px;
  min-height: 200px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

#w_close_btn_th {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  cursor: pointer;
  font-size: 16px;
  z-index: 1001;
}

#w_close_btn_th:hover {
  background: #d32f2f;
  transform: scale(1.1);
  transition: all 0.2s ease;
}

#w_update_form_th {
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
}

#w_update_tt {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  z-index: 1000;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  max-width: 700px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

#w_update_tt .update-form {
  padding: 15px;
  width: 100%;
}

#close-btn-tt {
  position: absolute;
  top: 10px;
  right: 15px;
  background-color: #ff4d4d;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

#close-btn-tt:hover {
  background-color: #ff0000;
}

#w_close_btn:hover,
#w_close_btn_th:hover,
#w_close_btn_nv:hover,
#w_close_btn_tt:hover,
#w_close_btn_hh:hover,
#w_close_btn_tthh:hover,
#w_close_btn_dvt:hover {
  background: #d32f2f;
  transform: scale(1.1);
  transition: all 0.2s ease;
}
