<?php
/**
 * HTTP Test for Thuộc Tính Update
 * This simulates real browser behavior
 */
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/thuoctinhCls.php';

// Get test data
$thuoctinhObj = new ThuocTinh();
$data = $thuoctinhObj->thuoctinhGetAll();
$testRecord = $data[0] ?? null;

if (!$testRecord) {
    die("❌ No test data available");
}

$testId = $testRecord->idThuocTinh;
$originalName = $testRecord->tenThuocTinh;
$originalNote = $testRecord->ghiChu;
?>

<!DOCTYPE html>
<html>
<head>
    <title>🌐 HTTP Test - Thuộc Tính Update</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .result { margin: 15px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        /* Popup styles */
        #test-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border: 2px solid #3498db;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            z-index: 9999;
            display: none;
            width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #f44336;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            cursor: pointer;
            border: none;
        }
    </style>
</head>
<body>
    <h1>🌐 HTTP Test - Thuộc Tính Update</h1>
    
    <div class="test-section">
        <h2>📋 Test Data</h2>
        <p><strong>Test ID:</strong> <?php echo $testId; ?></p>
        <p><strong>Original Name:</strong> <?php echo htmlspecialchars($originalName); ?></p>
        <p><strong>Original Note:</strong> <?php echo htmlspecialchars($originalNote); ?></p>
    </div>
    
    <div class="test-section">
        <h2>🧪 HTTP Tests</h2>
        
        <button class="btn btn-primary" onclick="testLoadForm()">1. Test Load Form</button>
        <button class="btn btn-success" onclick="testUpdateAjax()">2. Test Update AJAX</button>
        <button class="btn btn-warning" onclick="testFullWorkflow()">3. Test Full Workflow</button>
        
        <div id="test-results"></div>
    </div>
    
    <!-- Test popup -->
    <div id="test-popup">
        <button class="close-btn" onclick="closePopup()">×</button>
        <div id="popup-content">
            <h3>Loading...</h3>
        </div>
    </div>

    <script>
    function addResult(message, type = 'info') {
        const resultDiv = document.getElementById('test-results');
        const timestamp = new Date().toLocaleTimeString();
        resultDiv.innerHTML += `<div class="result ${type}">[${timestamp}] ${message}</div>`;
        resultDiv.scrollTop = resultDiv.scrollHeight;
    }
    
    function closePopup() {
        document.getElementById('test-popup').style.display = 'none';
    }
    
    function testLoadForm() {
        addResult('🧪 Testing form load...', 'info');
        
        $.ajax({
            url: './elements_LQA/mthuoctinh/thuoctinhUpdate.php',
            type: 'POST',
            data: { idThuocTinh: <?php echo $testId; ?> },
            success: function(response) {
                if (response && response.length > 1000) {
                    addResult('✅ Form loaded successfully (' + response.length + ' bytes)', 'success');
                    
                    // Show in popup
                    document.getElementById('popup-content').innerHTML = response;
                    document.getElementById('test-popup').style.display = 'block';
                    
                    // Check if form elements exist
                    if (response.includes('name="tenThuocTinh"')) {
                        addResult('✅ Form field "tenThuocTinh" found', 'success');
                    } else {
                        addResult('❌ Form field "tenThuocTinh" not found', 'error');
                    }
                    
                    if (response.includes('id="update-form"')) {
                        addResult('✅ Form ID "update-form" found', 'success');
                    } else {
                        addResult('❌ Form ID "update-form" not found', 'error');
                    }
                } else {
                    addResult('❌ Form load failed or empty response', 'error');
                }
            },
            error: function(xhr, status, error) {
                addResult('❌ Form load error: ' + error, 'error');
            }
        });
    }
    
    function testUpdateAjax() {
        addResult('🧪 Testing AJAX update...', 'info');
        
        const testName = '<?php echo $originalName; ?> [HTTP TEST]';
        const testNote = '<?php echo $originalNote; ?> [HTTP TEST]';
        
        const formData = new FormData();
        formData.append('idThuocTinh', '<?php echo $testId; ?>');
        formData.append('tenThuocTinh', testName);
        formData.append('ghiChu', testNote);
        
        $.ajax({
            url: './elements_LQA/mthuoctinh/thuoctinhAct.php?reqact=updatethuoctinh',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function(response) {
                if (response && response.success) {
                    addResult('✅ Update successful: ' + response.message, 'success');
                    addResult('📊 Rows affected: ' + response.rows_affected, 'info');
                    
                    // Restore original data
                    setTimeout(function() {
                        restoreOriginalData();
                    }, 1000);
                } else {
                    addResult('❌ Update failed: ' + (response ? response.message : 'Unknown error'), 'error');
                }
            },
            error: function(xhr, status, error) {
                addResult('❌ AJAX error: ' + error, 'error');
                addResult('📄 Response: ' + xhr.responseText.substring(0, 200), 'info');
            }
        });
    }
    
    function restoreOriginalData() {
        addResult('🔄 Restoring original data...', 'info');
        
        const formData = new FormData();
        formData.append('idThuocTinh', '<?php echo $testId; ?>');
        formData.append('tenThuocTinh', '<?php echo $originalName; ?>');
        formData.append('ghiChu', '<?php echo $originalNote; ?>');
        
        $.ajax({
            url: './elements_LQA/mthuoctinh/thuoctinhAct.php?reqact=updatethuoctinh',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function(response) {
                if (response && response.success) {
                    addResult('✅ Original data restored', 'success');
                } else {
                    addResult('⚠️ Failed to restore original data', 'error');
                }
            },
            error: function(xhr, status, error) {
                addResult('❌ Restore error: ' + error, 'error');
            }
        });
    }
    
    function testFullWorkflow() {
        addResult('🚀 Testing full workflow...', 'info');
        
        // Step 1: Load form
        addResult('Step 1: Loading form...', 'info');
        $.ajax({
            url: './elements_LQA/mthuoctinh/thuoctinhUpdate.php',
            type: 'POST',
            data: { idThuocTinh: <?php echo $testId; ?> },
            success: function(response) {
                if (response && response.length > 1000) {
                    addResult('✅ Step 1 complete: Form loaded', 'success');
                    
                    // Step 2: Update data
                    setTimeout(function() {
                        addResult('Step 2: Updating data...', 'info');
                        
                        const formData = new FormData();
                        formData.append('idThuocTinh', '<?php echo $testId; ?>');
                        formData.append('tenThuocTinh', '<?php echo $originalName; ?> [WORKFLOW TEST]');
                        formData.append('ghiChu', '<?php echo $originalNote; ?> [WORKFLOW TEST]');
                        
                        $.ajax({
                            url: './elements_LQA/mthuoctinh/thuoctinhAct.php?reqact=updatethuoctinh',
                            type: 'POST',
                            data: formData,
                            contentType: false,
                            processData: false,
                            dataType: 'json',
                            success: function(response) {
                                if (response && response.success) {
                                    addResult('✅ Step 2 complete: Data updated', 'success');
                                    
                                    // Step 3: Restore
                                    setTimeout(function() {
                                        addResult('Step 3: Restoring original data...', 'info');
                                        restoreOriginalData();
                                        
                                        setTimeout(function() {
                                            addResult('🎉 Full workflow test completed!', 'success');
                                        }, 1000);
                                    }, 1000);
                                } else {
                                    addResult('❌ Step 2 failed: ' + (response ? response.message : 'Unknown error'), 'error');
                                }
                            },
                            error: function(xhr, status, error) {
                                addResult('❌ Step 2 error: ' + error, 'error');
                            }
                        });
                    }, 1000);
                } else {
                    addResult('❌ Step 1 failed: Form load error', 'error');
                }
            },
            error: function(xhr, status, error) {
                addResult('❌ Step 1 error: ' + error, 'error');
            }
        });
    }
    
    $(document).ready(function() {
        addResult('🌐 HTTP Test initialized', 'info');
        addResult('Ready to test thuộc tính update functionality', 'info');
    });
    </script>
</body>
</html>
