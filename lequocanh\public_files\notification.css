/* CSS cho dropdown thông báo */
.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    display: none;
    padding: 0;
    margin-top: 10px;
}

.notification-dropdown.show {
    display: block;
}

.notification-header {
    display: flex;
    flex-direction: column;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.notification-header h6 {
    margin: 0 0 8px 0;
    font-weight: 600;
    font-size: 16px;
}

.notification-actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.notification-header .mark-all-read,
.notification-header .delete-read-notifications {
    font-size: 13px;
    color: #007bff;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
}

.notification-header .delete-read-notifications {
    color: #dc3545;
}

.notification-header .mark-all-read:hover,
.notification-header .delete-read-notifications:hover {
    text-decoration: underline;
}

.notification-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.notification-item {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: flex-start;
    transition: background-color 0.2s;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #f0f7ff;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    color: white;
}

.notification-icon.bg-warning {
    background-color: #ffc107;
}

.notification-icon.bg-success {
    background-color: #28a745;
}

.notification-icon.bg-danger {
    background-color: #dc3545;
}

.notification-icon.bg-secondary {
    background-color: #6c757d;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-title .badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
}

.notification-info {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.notification-info p {
    margin: 0 0 3px 0;
}

.notification-time {
    font-size: 11px;
    color: #adb5bd;
}

.notification-actions {
    margin-top: 8px;
    display: flex;
    gap: 8px;
}

.notification-actions a,
.notification-actions button {
    font-size: 12px;
    padding: 3px 8px;
}

.notification-empty {
    padding: 20px;
    text-align: center;
    color: #6c757d;
}

.notification-empty i {
    font-size: 24px;
    margin-bottom: 10px;
    color: #adb5bd;
}

.notification-footer {
    padding: 10px;
    display: flex;
    justify-content: space-around;
    border-top: 1px solid #eee;
}

.notification-footer a {
    font-size: 13px;
    color: #007bff;
    text-decoration: none;
}

.notification-footer a:hover {
    text-decoration: underline;
}

/* Thêm mũi tên chỉ lên trên dropdown */
.notification-dropdown::before {
    content: '';
    position: absolute;
    top: -10px;
    right: 18px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid white;
}

/* Modal chi tiết đơn hàng */
.order-detail-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    overflow-y: auto;
}

.order-detail-modal.show {
    display: block;
}

.order-detail-content {
    position: relative;
    background-color: white;
    margin: 50px auto;
    padding: 20px;
    border-radius: 8px;
    max-width: 800px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.order-detail-close {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}

.order-detail-close:hover {
    color: #343a40;
}

.order-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.order-detail-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.order-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    color: white;
}

.order-status.warning {
    background-color: #ffc107;
}

.order-status.success {
    background-color: #28a745;
}

.order-status.danger {
    background-color: #dc3545;
}

.order-detail-info {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.order-detail-info-col {
    flex: 1;
    min-width: 250px;
    margin-bottom: 15px;
}

.order-detail-info-item {
    margin-bottom: 10px;
}

.order-detail-info-item strong {
    display: block;
    margin-bottom: 5px;
    color: #495057;
}

.order-detail-address {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    margin-top: 5px;
}

.order-detail-items {
    margin-bottom: 20px;
}

.order-detail-items table {
    width: 100%;
    border-collapse: collapse;
}

.order-detail-items th,
.order-detail-items td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.order-detail-items th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.order-detail-items .product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 5px;
}

.order-detail-items .product-name {
    font-weight: 500;
}

.order-detail-total {
    text-align: right;
    font-size: 18px;
    font-weight: 600;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* Responsive */
@media (max-width: 576px) {
    .notification-dropdown {
        width: 300px;
        right: -100px;
    }

    .notification-dropdown::before {
        right: 110px;
    }

    .order-detail-content {
        margin: 20px;
        padding: 15px;
    }

    .order-detail-info-col {
        flex: 100%;
    }
}
