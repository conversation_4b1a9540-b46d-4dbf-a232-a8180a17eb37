<?php
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/thuoctinhCls.php';

echo "<h1>🔧 DEBUG CẬP NHẬT THUỘC TÍNH</h1>";

// Test AJAX endpoint
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_ajax'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $thuoctinhObj = new ThuocTinh();
        $idThuocTinh = $_POST['idThuocTinh'];
        $tenThuocTinh = $_POST['tenThuocTinh'];
        $ghiChu = $_POST['ghiChu'];
        
        // Lấy hình ảnh cũ
        $thuoctinhInfo = $thuoctinhObj->thuoctinhGetbyId($idThuocTinh);
        $hinhanh = $thuoctinhInfo ? $thuoctinhInfo->hinhanh : '';
        
        $result = $thuoctinhObj->thuoctinhUpdate($tenThuocTinh, $ghiChu, $hinhanh, $idThuocTinh);
        
        echo json_encode([
            'success' => $result > 0,
            'message' => $result > 0 ? 'Cập nhật thành công!' : 'Không có thay đổi nào',
            'rows_affected' => $result
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Lỗi: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

try {
    $thuoctinhObj = new ThuocTinh();
    
    // Lấy danh sách thuộc tính
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>1. DANH SÁCH THUỘC TÍNH:</h2>";
    
    $stmt = $conn->query("SELECT idThuocTinh, tenThuocTinh, ghiChu FROM thuoctinh LIMIT 5");
    $thuoctinhList = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($thuoctinhList) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Tên</th><th>Ghi chú</th><th>Action</th></tr>";
        foreach ($thuoctinhList as $item) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($item['idThuocTinh']) . "</td>";
            echo "<td>" . htmlspecialchars($item['tenThuocTinh']) . "</td>";
            echo "<td>" . htmlspecialchars($item['ghiChu']) . "</td>";
            echo "<td><button onclick='testUpdate(" . $item['idThuocTinh'] . ", \"" . htmlspecialchars($item['tenThuocTinh']) . "\", \"" . htmlspecialchars($item['ghiChu']) . "\")'>Test Update</button></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Không có thuộc tính nào.</p>";
    }
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>2. TEST AJAX ENDPOINT:</h2>";
    echo "<div id='test-result'></div>";
    echo "</div>";
    
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>3. KIỂM TRA ĐƯỜNG DẪN:</h2>";
    echo "<p><strong>Current directory:</strong> " . __DIR__ . "</p>";
    echo "<p><strong>thuoctinhAct.php exists:</strong> " . (file_exists(__DIR__ . '/elements_LQA/mthuoctinh/thuoctinhAct.php') ? 'YES' : 'NO') . "</p>";
    echo "<p><strong>thuoctinhUpdate.php exists:</strong> " . (file_exists(__DIR__ . '/elements_LQA/mthuoctinh/thuoctinhUpdate.php') ? 'YES' : 'NO') . "</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h2>❌ LỖI:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testUpdate(id, ten, ghiChu) {
    console.log('Testing update for ID:', id);
    
    $('#test-result').html('<div style="color: blue;">Đang test...</div>');
    
    $.ajax({
        url: './test_thuoctinh_update_debug.php',
        type: 'POST',
        data: {
            test_ajax: true,
            idThuocTinh: id,
            tenThuocTinh: ten + ' (Updated)',
            ghiChu: ghiChu + ' (Test update)'
        },
        dataType: 'json',
        success: function(response) {
            console.log('Test response:', response);
            
            if (response.success) {
                $('#test-result').html('<div style="color: green;">✅ Test thành công: ' + response.message + '</div>');
            } else {
                $('#test-result').html('<div style="color: red;">❌ Test thất bại: ' + response.message + '</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Test error:', error);
            console.error('Response:', xhr.responseText);
            $('#test-result').html('<div style="color: red;">❌ Lỗi AJAX: ' + error + '<br>Response: ' + xhr.responseText + '</div>');
        }
    });
}

// Test đường dẫn AJAX
$(document).ready(function() {
    console.log('Testing AJAX paths...');
    
    // Test thuoctinhAct.php
    $.ajax({
        url: './elements_LQA/mthuoctinh/thuoctinhAct.php',
        type: 'GET',
        success: function() {
            console.log('✅ thuoctinhAct.php accessible');
        },
        error: function(xhr) {
            console.error('❌ thuoctinhAct.php not accessible:', xhr.status);
        }
    });
    
    // Test thuoctinhUpdate.php
    $.ajax({
        url: './elements_LQA/mthuoctinh/thuoctinhUpdate.php',
        type: 'POST',
        data: { idThuocTinh: 1 },
        success: function() {
            console.log('✅ thuoctinhUpdate.php accessible');
        },
        error: function(xhr) {
            console.error('❌ thuoctinhUpdate.php not accessible:', xhr.status);
        }
    });
});
</script>

<style>
table {
    border-collapse: collapse;
    width: 100%;
}
th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}
th {
    background-color: #f2f2f2;
}
button {
    padding: 5px 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}
button:hover {
    background-color: #0056b3;
}
</style>
