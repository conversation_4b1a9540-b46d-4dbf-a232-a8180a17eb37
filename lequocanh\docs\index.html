<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📚 Docs - <PERSON><PERSON><PERSON> l<PERSON> h<PERSON> thống</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .docs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .doc-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid #28a745;
        }
        
        .doc-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .doc-card.explanation {
            border-left-color: #ffc107;
        }
        
        .doc-card.guide {
            border-left-color: #17a2b8;
        }
        
        .doc-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.4em;
        }
        
        .doc-card p {
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .doc-link {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        
        .doc-link:hover {
            background: #218838;
            color: white;
            text-decoration: none;
        }
        
        .doc-link.explanation {
            background: #ffc107;
            color: #212529;
        }
        
        .doc-link.explanation:hover {
            background: #e0a800;
            color: #212529;
        }
        
        .doc-link.guide {
            background: #17a2b8;
        }
        
        .doc-link.guide:hover {
            background: #138496;
        }
        
        .navigation {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .nav-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            margin: 5px;
            transition: background-color 0.3s ease;
        }
        
        .nav-link:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        .overview {
            background: #fff3cd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #ffc107;
        }
        
        .overview h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .overview ul {
            color: #856404;
            margin: 0;
        }
        
        .overview li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 Documentation</h1>
        <p>Tài liệu hướng dẫn và giải thích hệ thống</p>
    </div>
    
    <div class="overview">
        <h3>📖 Tổng quan tài liệu</h3>
        <ul>
            <li>📊 <strong>Hệ thống thống kê:</strong> Cách hoạt động và sử dụng hệ thống thống kê hoạt động nhân viên</li>
            <li>🔍 <strong>Giải quyết vấn đề:</strong> Phân tích và giải thích các vấn đề thường gặp</li>
            <li>🛠️ <strong>Hướng dẫn debug:</strong> Cách sử dụng các công cụ debug và test</li>
            <li>💡 <strong>Best practices:</strong> Các phương pháp tốt nhất khi làm việc với hệ thống</li>
        </ul>
    </div>
    
    <div class="docs-grid">
        <div class="doc-card guide">
            <h3>📊 Giải thích hệ thống thống kê</h3>
            <p>Tài liệu chi tiết về cách hoạt động của hệ thống thống kê hoạt động nhân viên, cấu trúc database, và hướng dẫn sử dụng.</p>
            <a href="giai_thich_thong_ke_hoat_dong.php" class="doc-link guide">Xem tài liệu</a>
        </div>
        
        <div class="doc-card explanation">
            <h3>🔍 Giải thích vấn đề biểu đồ</h3>
            <p>Phân tích chi tiết vấn đề biểu đồ hiển thị dữ liệu mặc dù bảng không có dữ liệu, nguyên nhân và giải pháp đã áp dụng.</p>
            <a href="giai_thich_van_de_bieu_do.php" class="doc-link explanation">Xem giải thích</a>
        </div>
    </div>
    
    <div class="navigation">
        <h3>🔗 Điều hướng</h3>
        <a href="../administrator/index.php?req=thongKeHoatDongView" class="nav-link">📊 Xem Thống Kê</a>
        <a href="../administrator/index.php?req=nhatKyHoatDongView" class="nav-link">📋 Xem Nhật Ký</a>
        <a href="../tools/" class="nav-link">🛠️ Công Cụ Debug</a>
        <a href="../" class="nav-link">🏠 Trang Chủ</a>
    </div>
</body>
</html>
