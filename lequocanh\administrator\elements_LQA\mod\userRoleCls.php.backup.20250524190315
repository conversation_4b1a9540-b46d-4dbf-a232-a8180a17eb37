<?php
$s = '../../elements_LQA/mod/database.php';
if (file_exists($s)) {
    $f = $s;
} else {
    $f = './elements_LQA/mod/database.php';
    if (!file_exists($f)) {
        $f = './administrator/elements_LQA/mod/database.php';
    }
}
require_once $f;

// Tìm đường dẫn đúng đến roleCls.php
$rolePaths = [
    '../../elements_LQA/mod/roleCls.php',
    './elements_LQA/mod/roleCls.php',
    './administrator/elements_LQA/mod/roleCls.php'
];

foreach ($rolePaths as $path) {
    if (file_exists($path)) {
        require_once $path;
        break;
    }
}

/**
 * Lớp UserRole - Quản lý vai trò người dùng
 * Lớp này cung cấp các phương thức để gán vai trò mặc định cho người dùng mới
 */
class UserRole
{
    private $db;
    private $roleManager;

    public function __construct()
    {
        $this->db = Database::getInstance()->getConnection();
        if (class_exists('Role')) {
            $this->roleManager = new Role();
        }
    }

    /**
     * Kiểm tra xem bảng roles đã tồn tại chưa
     */
    private function rolesTableExists()
    {
        try {
            $sql = "SHOW TABLES LIKE 'roles'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Lỗi kiểm tra bảng roles: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Kiểm tra xem bảng user_roles đã tồn tại chưa
     */
    private function userRolesTableExists()
    {
        try {
            $sql = "SHOW TABLES LIKE 'user_roles'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Lỗi kiểm tra bảng user_roles: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Gán vai trò mặc định cho người dùng mới
     * @param int $userId ID của người dùng
     * @param string $roleName Tên vai trò (mặc định là 'customer')
     * @return bool Kết quả gán vai trò
     */
    public function assignDefaultRole($userId, $roleName = 'customer')
    {
        // Kiểm tra xem bảng roles và user_roles đã tồn tại chưa
        if (!$this->roleManager) {
            error_log("Không tìm thấy Role Manager");
            return false;
        }

        // Đảm bảo các bảng cần thiết đã được tạo
        if (!$this->rolesTableExists() || !$this->userRolesTableExists()) {
            error_log("Bảng roles hoặc user_roles chưa tồn tại, đang tạo...");
            // Tạo bảng nếu chưa tồn tại
            $this->createTablesIfNotExist();
        }

        try {
            // Lấy ID của vai trò
            $role = $this->roleManager->getRoleByName($roleName);

            // Nếu vai trò không tồn tại, tạo mới
            if (!$role) {
                error_log("Không tìm thấy vai trò: $roleName, đang tạo mới...");

                // Tạo mô tả cho vai trò tùy theo loại
                $description = '';
                switch ($roleName) {
                    case 'admin':
                        $description = 'Quản trị viên - có toàn quyền trên hệ thống';
                        break;
                    case 'staff':
                        $description = 'Nhân viên - có quyền quản lý sản phẩm, đơn hàng';
                        break;
                    case 'customer':
                        $description = 'Khách hàng - chỉ có quyền mua hàng và quản lý tài khoản cá nhân';
                        break;
                    default:
                        $description = 'Vai trò tùy chỉnh';
                }

                // Thêm vai trò mới
                $addResult = $this->roleManager->addRole($roleName, $description);
                if (!$addResult) {
                    error_log("Không thể tạo vai trò mới: $roleName");
                    return false;
                }

                // Lấy lại vai trò vừa tạo
                $role = $this->roleManager->getRoleByName($roleName);
                if (!$role) {
                    error_log("Không thể lấy vai trò vừa tạo: $roleName");
                    return false;
                }
            }

            // Gán vai trò cho người dùng
            $result = $this->roleManager->assignRoleToUser($userId, $role->id);
            if ($result) {
                error_log("Đã gán vai trò $roleName (ID: {$role->id}) cho người dùng ID: $userId");
            } else {
                error_log("Không thể gán vai trò $roleName (ID: {$role->id}) cho người dùng ID: $userId");
            }
            return $result;
        } catch (Exception $e) {
            error_log("Lỗi khi gán vai trò mặc định: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Tạo các bảng cần thiết nếu chưa tồn tại
     */
    private function createTablesIfNotExist()
    {
        try {
            // Tạo bảng roles nếu chưa tồn tại
            if (!$this->rolesTableExists()) {
                $createRolesTable = "CREATE TABLE `roles` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `role_name` varchar(50) NOT NULL,
                    `description` text,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `role_name` (`role_name`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

                $this->db->exec($createRolesTable);
                error_log("Đã tạo bảng roles");

                // Thêm các vai trò mặc định
                $insertDefaultRoles = "INSERT INTO `roles` (`role_name`, `description`) VALUES
                    ('admin', 'Quản trị viên - có toàn quyền trên hệ thống'),
                    ('staff', 'Nhân viên - có quyền quản lý sản phẩm, đơn hàng'),
                    ('customer', 'Khách hàng - chỉ có quyền mua hàng và quản lý tài khoản cá nhân');";

                $this->db->exec($insertDefaultRoles);
                error_log("Đã thêm các vai trò mặc định");
            }

            // Tạo bảng user_roles nếu chưa tồn tại
            if (!$this->userRolesTableExists()) {
                $createUserRolesTable = "CREATE TABLE `user_roles` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `user_id` int(11) NOT NULL,
                    `role_id` int(11) NOT NULL,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `user_role_unique` (`user_id`,`role_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

                $this->db->exec($createUserRolesTable);
                error_log("Đã tạo bảng user_roles");
            }

            return true;
        } catch (PDOException $e) {
            error_log("Lỗi khi tạo bảng: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Gán vai trò nhân viên cho người dùng
     * @param int $userId ID của người dùng
     * @return bool Kết quả gán vai trò
     */
    public function assignStaffRole($userId)
    {
        return $this->assignDefaultRole($userId, 'staff');
    }

    /**
     * Gán vai trò admin cho người dùng
     * @param int $userId ID của người dùng
     * @return bool Kết quả gán vai trò
     */
    public function assignAdminRole($userId)
    {
        return $this->assignDefaultRole($userId, 'admin');
    }
}
