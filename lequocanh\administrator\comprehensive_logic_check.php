<?php
/**
 * KIỂM TRA TỔNG HỢP LOGIC ĐỒNG NHẤT
 * <PERSON><PERSON>m bảo logic giữa Bảng Đơn Giá và Phiếu Nhập hoạt động nhất quán
 */

echo "<h1>🔍 KIỂM TRA LOGIC TỔNG HỢP</h1>";
echo "<p><strong><PERSON><PERSON><PERSON> đích:</strong> Đảm bảo logic giữa Bảng Đơn Giá và Phiếu Nhập hoạt động nhất quán</p>";
echo "<hr>";

// Include các class cần thiết
require_once './elements_LQA/mod/database.php';
require_once './elements_LQA/mod/hanghoaCls.php';
require_once './elements_LQA/mod/dongiaCls.php';
require_once './elements_LQA/config/price_logic_config.php';

echo "<h2>📋 1. KIỂM TRA CẤU HÌNH HIỆN TẠI</h2>";
$config = PriceLogicConfig::getCurrentConfig();

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
echo "<tr style='background: #f8f9fa;'><th>Cấu hình</th><th>Giá trị</th><th>Ý nghĩa</th><th>Ảnh hưởng</th></tr>";

$configDetails = [
    'auto_update_price_on_import' => [
        'name' => 'Tự động cập nhật giá khi duyệt phiếu nhập',
        'meaning' => $config['auto_update_price_on_import'] ? 'SẼ cập nhật giathamkhao' : 'KHÔNG cập nhật giathamkhao',
        'impact' => $config['auto_update_price_on_import'] ? '⚠️ Có thể ghi đè giá hiện tại' : '✅ An toàn, bảo vệ giá hiện tại'
    ],
    'override_existing_price' => [
        'name' => 'Ghi đè giá đã có',
        'meaning' => $config['override_existing_price'] ? 'Ghi đè ngay cả khi có đơn giá' : 'Chỉ cập nhật khi chưa có đơn giá',
        'impact' => $config['override_existing_price'] ? '⚠️ Nguy hiểm - có thể mất đơn giá thủ công' : '✅ An toàn - bảo vệ đơn giá thủ công'
    ],
    'create_price_from_import' => [
        'name' => 'Tạo đơn giá từ phiếu nhập',
        'meaning' => $config['create_price_from_import'] ? 'Tự động tạo đơn giá mới' : 'Không tạo đơn giá mới',
        'impact' => $config['create_price_from_import'] ? '✅ Tiện lợi cho sản phẩm mới' : '⚠️ Cần tạo đơn giá thủ công'
    ],
    'auto_apply_profit_margin' => [
        'name' => 'Tự động áp dụng lợi nhuận',
        'meaning' => $config['auto_apply_profit_margin'] ? 'Giá bán = Giá nhập + ' . $config['default_profit_margin'] . '%' : 'Giá bán = Giá nhập',
        'impact' => $config['auto_apply_profit_margin'] ? '✅ Tự động tính lợi nhuận' : '⚠️ Giá bán = giá nhập'
    ]
];

foreach ($configDetails as $key => $detail) {
    $value = $config[$key];
    $displayValue = is_bool($value) ? ($value ? '✅ BẬT' : '❌ TẮT') : $value;
    if ($key === 'default_profit_margin') {
        $displayValue = $value . '%';
    }
    
    echo "<tr>";
    echo "<td><strong>" . $detail['name'] . "</strong></td>";
    echo "<td style='text-align: center;'>" . $displayValue . "</td>";
    echo "<td>" . $detail['meaning'] . "</td>";
    echo "<td>" . $detail['impact'] . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🔄 2. KIỂM TRA LOGIC TƯƠNG TÁC</h2>";

// Test các scenario
$scenarios = [
    [
        'name' => 'Sản phẩm mới (chưa có đơn giá)',
        'hasActivePrice' => false,
        'description' => 'Sản phẩm chưa từng có đơn giá nào'
    ],
    [
        'name' => 'Sản phẩm đã có đơn giá đang áp dụng',
        'hasActivePrice' => true,
        'description' => 'Sản phẩm đã có đơn giá với apDung = 1'
    ]
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
echo "<tr style='background: #f8f9fa;'><th>Tình huống</th><th>Cập nhật giathamkhao?</th><th>Tạo đơn giá mới?</th><th>Kết quả</th></tr>";

foreach ($scenarios as $scenario) {
    $shouldUpdate = PriceLogicConfig::shouldUpdateReferencePrice($scenario['hasActivePrice']);
    $shouldCreate = PriceLogicConfig::shouldCreatePriceFromImport() && !$scenario['hasActivePrice'];
    
    echo "<tr>";
    echo "<td><strong>" . $scenario['name'] . "</strong><br><small>" . $scenario['description'] . "</small></td>";
    echo "<td style='text-align: center; color: " . ($shouldUpdate ? 'green' : 'red') . ";'>" . ($shouldUpdate ? '✅ CÓ' : '❌ KHÔNG') . "</td>";
    echo "<td style='text-align: center; color: " . ($shouldCreate ? 'green' : 'red') . ";'>" . ($shouldCreate ? '✅ CÓ' : '❌ KHÔNG') . "</td>";
    
    $result = '';
    if ($scenario['hasActivePrice']) {
        if ($shouldUpdate) {
            $result = '⚠️ GHI ĐÈ giá đã có';
        } else {
            $result = '✅ BẢO VỆ giá đã có';
        }
    } else {
        if ($shouldCreate) {
            $result = '✅ TẠO đơn giá mới';
        } else {
            $result = '⚠️ Sản phẩm vẫn KHÔNG có giá';
        }
    }
    echo "<td>" . $result . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🧮 3. KIỂM TRA TÍNH TOÁN GIÁ</h2>";

$testPrices = [50000, 100000, 250000, 500000, 1000000];
echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
echo "<tr style='background: #f8f9fa;'><th>Giá nhập</th><th>Tỷ lệ lợi nhuận</th><th>Giá bán tính được</th><th>Lợi nhuận (VNĐ)</th></tr>";

foreach ($testPrices as $importPrice) {
    $sellingPrice = PriceLogicConfig::calculateSellingPrice($importPrice);
    $profit = $sellingPrice - $importPrice;
    
    echo "<tr>";
    echo "<td>" . number_format($importPrice) . " VNĐ</td>";
    echo "<td>" . $config['default_profit_margin'] . "%</td>";
    echo "<td><strong>" . number_format($sellingPrice) . " VNĐ</strong></td>";
    echo "<td style='color: green;'>+" . number_format($profit) . " VNĐ</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🔍 4. KIỂM TRA NHẤT QUÁN LOGIC</h2>";

// Kiểm tra logic trong DongiaAdd
echo "<h3>4.1. Logic trong DongiaAdd (Bảng Đơn Giá)</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin-bottom: 15px;'>";
echo "<strong>Khi thêm đơn giá mới:</strong><br>";
echo "1. ✅ Đặt tất cả đơn giá cũ thành apDung = 0<br>";
echo "2. ✅ Thêm đơn giá mới với apDung = 1<br>";
echo "3. ✅ Cập nhật giathamkhao = giaBan mới<br>";
echo "<strong>➡️ Kết quả:</strong> Đơn giá mới được áp dụng ngay lập tức";
echo "</div>";

// Kiểm tra logic trong phiếu nhập
echo "<h3>4.2. Logic trong Phiếu Nhập</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #28a745; margin-bottom: 15px;'>";
echo "<strong>Khi duyệt phiếu nhập:</strong><br>";
echo "1. ✅ Kiểm tra sản phẩm có đơn giá đang áp dụng không<br>";
echo "2. ✅ Nếu CHƯA có → Tạo đơn giá mới (nếu cấu hình cho phép)<br>";
echo "3. ✅ Nếu ĐÃ có → Bảo vệ, không ghi đè (trừ khi cấu hình cho phép)<br>";
echo "4. ✅ Áp dụng tỷ lệ lợi nhuận tự động<br>";
echo "<strong>➡️ Kết quả:</strong> Đơn giá thủ công được bảo vệ";
echo "</div>";

echo "<h2>⚖️ 5. ĐÁNH GIÁ TÍNH NHẤT QUÁN</h2>";

$consistency_checks = [
    [
        'check' => 'Cả 2 logic đều sử dụng PriceLogicConfig',
        'status' => true,
        'detail' => 'Phiếu nhập và Đơn giá đều tuân theo cùng cấu hình'
    ],
    [
        'check' => 'Cả 2 đều cập nhật giathamkhao khi cần',
        'status' => true,
        'detail' => 'Đồng bộ giữa bảng dongia và hanghoa'
    ],
    [
        'check' => 'Cả 2 đều áp dụng quy tắc "1 đơn giá active"',
        'status' => true,
        'detail' => 'DongiaSetAllToFalse() được gọi trước khi thêm mới'
    ],
    [
        'check' => 'Cả 2 đều có error logging',
        'status' => true,
        'detail' => 'Ghi log chi tiết để debug'
    ],
    [
        'check' => 'Logic bảo vệ đơn giá thủ công',
        'status' => !$config['auto_update_price_on_import'] || !$config['override_existing_price'],
        'detail' => $config['auto_update_price_on_import'] && $config['override_existing_price'] ? 'CẢNH BÁO: Có thể ghi đè đơn giá thủ công' : 'An toàn: Đơn giá thủ công được bảo vệ'
    ]
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
echo "<tr style='background: #f8f9fa;'><th>Kiểm tra</th><th>Trạng thái</th><th>Chi tiết</th></tr>";

foreach ($consistency_checks as $check) {
    $statusIcon = $check['status'] ? '✅ PASS' : '❌ FAIL';
    $statusColor = $check['status'] ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td>" . $check['check'] . "</td>";
    echo "<td style='text-align: center; color: " . $statusColor . ";'><strong>" . $statusIcon . "</strong></td>";
    echo "<td>" . $check['detail'] . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>📊 6. KẾT LUẬN VÀ KHUYẾN NGHỊ</h2>";

$isConfigSafe = !$config['auto_update_price_on_import'] || !$config['override_existing_price'];
$configRating = $isConfigSafe ? 'AN TOÀN' : 'CẦN ĐIỀU CHỈNH';
$configColor = $isConfigSafe ? 'green' : 'orange';

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h3 style='color: " . $configColor . ";'>🎯 ĐÁNH GIÁ TỔNG QUAN: " . $configRating . "</h3>";

if ($isConfigSafe) {
    echo "<div style='color: green;'>";
    echo "<h4>✅ LOGIC HOẠT ĐỘNG NHẤT QUÁN VÀ AN TOÀN:</h4>";
    echo "<ul>";
    echo "<li><strong>Bảo vệ đơn giá thủ công:</strong> Đơn giá đã thiết lập không bị ghi đè</li>";
    echo "<li><strong>Tự động hóa thông minh:</strong> Chỉ tạo đơn giá cho sản phẩm mới</li>";
    echo "<li><strong>Tính toán chính xác:</strong> Áp dụng lợi nhuận " . $config['default_profit_margin'] . "% tự động</li>";
    echo "<li><strong>Đồng bộ dữ liệu:</strong> Bảng dongia và hanghoa luôn nhất quán</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='color: orange;'>";
    echo "<h4>⚠️ CẦN ĐIỀU CHỈNH CẤU HÌNH:</h4>";
    echo "<ul>";
    echo "<li><strong>Nguy cơ ghi đè:</strong> Đơn giá thủ công có thể bị mất</li>";
    echo "<li><strong>Khuyến nghị:</strong> Tắt AUTO_UPDATE_PRICE_ON_IMPORT hoặc OVERRIDE_EXISTING_PRICE</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h4>🔧 KHUYẾN NGHỊ CẤU HÌNH TỐI ƯU:</h4>";
echo "<pre style='background: #e9ecef; padding: 15px; border-radius: 4px;'>";
echo "AUTO_UPDATE_PRICE_ON_IMPORT = false;  // Không tự động cập nhật\n";
echo "OVERRIDE_EXISTING_PRICE = false;     // Không ghi đè giá đã có\n";
echo "CREATE_PRICE_FROM_IMPORT = true;     // Tạo giá cho sản phẩm mới\n";
echo "DEFAULT_PROFIT_MARGIN = 20;          // Lợi nhuận 20%\n";
echo "AUTO_APPLY_PROFIT_MARGIN = true;     // Tự động tính lợi nhuận";
echo "</pre>";

echo "<h4>📋 WORKFLOW KHUYẾN NGHỊ:</h4>";
echo "<ol>";
echo "<li><strong>Thiết lập đơn giá thủ công</strong> cho sản phẩm quan trọng</li>";
echo "<li><strong>Để hệ thống tự động</strong> tạo giá cho sản phẩm mới từ phiếu nhập</li>";
echo "<li><strong>Kiểm tra định kỳ</strong> sản phẩm chưa có giá</li>";
echo "<li><strong>Theo dõi log</strong> khi duyệt phiếu nhập</li>";
echo "</ol>";

echo "</div>";

echo "<p><em>Kiểm tra hoàn thành lúc: " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px 12px; border: 1px solid #ddd; text-align: left; }
th { background-color: #f8f9fa; font-weight: bold; }
tr:nth-child(even) { background-color: #f9f9f9; }
h1, h2, h3 { color: #333; }
h1 { border-bottom: 3px solid #007bff; padding-bottom: 10px; }
h2 { border-bottom: 2px solid #28a745; padding-bottom: 8px; margin-top: 30px; }
pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
</style>
