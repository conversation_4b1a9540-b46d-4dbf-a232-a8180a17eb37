<?php
/**
 * File Test Logic Giá <PERSON>
 * 
 * Chạy file này để kiểm tra logic quản lý giá đã được cập nhật đúng chưa
 */

require_once './elements_LQA/mod/database.php';
require_once './elements_LQA/mod/hanghoaCls.php';
require_once './elements_LQA/mod/dongiaCls.php';
require_once './elements_LQA/config/price_logic_config.php';

echo "<h1>Test Logic Quản Lý Giá Mới</h1>";
echo "<hr>";

// Test 1: Kiểm tra cấu hình
echo "<h2>1. Kiểm tra cấu hình hiện tại:</h2>";
$config = PriceLogicConfig::getCurrentConfig();
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>C<PERSON><PERSON> hình</th><th><PERSON><PERSON><PERSON> trị</th><th>Ý nghĩa</th></tr>";
echo "<tr><td>Tự động cập nhật giá</td><td>" . ($config['auto_update_price_on_import'] ? 'BẬT' : 'TẮT') . "</td><td>" . ($config['auto_update_price_on_import'] ? 'Sẽ cập nhật giá khi duyệt phiếu nhập' : 'KHÔNG cập nhật giá khi duyệt phiếu nhập') . "</td></tr>";
echo "<tr><td>Ghi đè giá đã có</td><td>" . ($config['override_existing_price'] ? 'BẬT' : 'TẮT') . "</td><td>" . ($config['override_existing_price'] ? 'Sẽ ghi đè giá đã có' : 'KHÔNG ghi đè giá đã có') . "</td></tr>";
echo "<tr><td>Tạo giá từ phiếu nhập</td><td>" . ($config['create_price_from_import'] ? 'BẬT' : 'TẮT') . "</td><td>" . ($config['create_price_from_import'] ? 'Sẽ tạo đơn giá mới cho sản phẩm chưa có giá' : 'KHÔNG tạo đơn giá mới') . "</td></tr>";
echo "<tr><td>Tỷ lệ lợi nhuận</td><td>" . $config['default_profit_margin'] . "%</td><td>Lợi nhuận được áp dụng khi tính giá bán</td></tr>";
echo "<tr><td>Tự động áp dụng lợi nhuận</td><td>" . ($config['auto_apply_profit_margin'] ? 'BẬT' : 'TẮT') . "</td><td>" . ($config['auto_apply_profit_margin'] ? 'Tự động tính giá bán = giá nhập + lợi nhuận' : 'Sử dụng giá nhập làm giá bán') . "</td></tr>";
echo "</table>";

// Test 2: Kiểm tra method mới
echo "<h2>2. Kiểm tra method DongiaGetActiveByProduct:</h2>";
try {
    $dongiaObj = new Dongia();
    
    // Lấy một sản phẩm bất kỳ để test
    $hanghoaObj = new hanghoa();
    $allProducts = $hanghoaObj->HanghoaGetAll();
    
    if (!empty($allProducts)) {
        $testProduct = $allProducts[0];
        echo "<p><strong>Test với sản phẩm:</strong> " . $testProduct->tenhanghoa . " (ID: " . $testProduct->idhanghoa . ")</p>";
        
        $activePrice = $dongiaObj->DongiaGetActiveByProduct($testProduct->idhanghoa);
        
        if ($activePrice) {
            echo "<p style='color: green;'>✓ Sản phẩm có đơn giá đang áp dụng: " . number_format($activePrice->giaBan) . " VNĐ</p>";
            echo "<p>Ngày áp dụng: " . $activePrice->ngayApDung . "</p>";
            echo "<p>Ngày kết thúc: " . $activePrice->ngayKetThuc . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Sản phẩm chưa có đơn giá đang áp dụng</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Không tìm thấy sản phẩm nào để test</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Lỗi khi test method: " . $e->getMessage() . "</p>";
}

// Test 3: Kiểm tra tính toán giá
echo "<h2>3. Test tính toán giá bán từ giá nhập:</h2>";
$testImportPrices = [100000, 250000, 500000, 1000000];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Giá nhập</th><th>Tỷ lệ lợi nhuận</th><th>Giá bán tính được</th></tr>";

foreach ($testImportPrices as $importPrice) {
    $sellingPrice = PriceLogicConfig::calculateSellingPrice($importPrice);
    echo "<tr>";
    echo "<td>" . number_format($importPrice) . " VNĐ</td>";
    echo "<td>" . $config['default_profit_margin'] . "%</td>";
    echo "<td>" . number_format($sellingPrice) . " VNĐ</td>";
    echo "</tr>";
}
echo "</table>";

// Test 4: Kiểm tra logic shouldUpdateReferencePrice
echo "<h2>4. Test logic cập nhật giá tham khảo:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Tình huống</th><th>Có đơn giá đang áp dụng</th><th>Kết quả</th><th>Giải thích</th></tr>";

$scenarios = [
    ['Sản phẩm mới', false],
    ['Sản phẩm đã có giá', true]
];

foreach ($scenarios as $scenario) {
    $shouldUpdate = PriceLogicConfig::shouldUpdateReferencePrice($scenario[1]);
    echo "<tr>";
    echo "<td>" . $scenario[0] . "</td>";
    echo "<td>" . ($scenario[1] ? 'Có' : 'Không') . "</td>";
    echo "<td style='color: " . ($shouldUpdate ? 'green' : 'red') . ";'>" . ($shouldUpdate ? 'CẬP NHẬT' : 'KHÔNG CẬP NHẬT') . "</td>";
    echo "<td>";
    if ($scenario[1] && !$config['override_existing_price']) {
        echo "Bảo vệ giá đã có";
    } elseif (!$config['auto_update_price_on_import']) {
        echo "Tắt tự động cập nhật";
    } else {
        echo "Cho phép cập nhật";
    }
    echo "</td>";
    echo "</tr>";
}
echo "</table>";

// Test 5: Kiểm tra file cấu hình
echo "<h2>5. Kiểm tra file cấu hình:</h2>";
$configFile = './elements_LQA/config/price_logic_config.php';
if (file_exists($configFile)) {
    echo "<p style='color: green;'>✓ File cấu hình tồn tại: " . $configFile . "</p>";
    echo "<p>Kích thước file: " . filesize($configFile) . " bytes</p>";
    echo "<p>Lần sửa đổi cuối: " . date('Y-m-d H:i:s', filemtime($configFile)) . "</p>";
} else {
    echo "<p style='color: red;'>✗ File cấu hình không tồn tại!</p>";
}

// Test 6: Kiểm tra class PriceLogicConfig
echo "<h2>6. Kiểm tra class PriceLogicConfig:</h2>";
if (class_exists('PriceLogicConfig')) {
    echo "<p style='color: green;'>✓ Class PriceLogicConfig đã được load</p>";
    
    $methods = get_class_methods('PriceLogicConfig');
    echo "<p><strong>Các method có sẵn:</strong></p>";
    echo "<ul>";
    foreach ($methods as $method) {
        echo "<li>" . $method . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>✗ Class PriceLogicConfig chưa được load!</p>";
}

echo "<hr>";
echo "<h2>Kết luận:</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<h3>Logic mới đã được triển khai với các cải tiến:</h3>";
echo "<ul>";
echo "<li>✓ Bảo vệ đơn giá đã thiết lập thủ công</li>";
echo "<li>✓ Cấu hình linh hoạt theo nhu cầu</li>";
echo "<li>✓ Tự động tính toán lợi nhuận</li>";
echo "<li>✓ Ghi log chi tiết các thao tác</li>";
echo "<li>✓ Phân biệt rõ giá nhập và giá bán</li>";
echo "</ul>";

echo "<h3>Khuyến nghị:</h3>";
echo "<ul>";
echo "<li>Kiểm tra cấu hình phù hợp với nhu cầu</li>";
echo "<li>Test trên dữ liệu thực tế</li>";
echo "<li>Theo dõi log khi duyệt phiếu nhập</li>";
echo "<li>Đào tạo nhân viên về logic mới</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Test hoàn thành lúc: " . date('Y-m-d H:i:s') . "</em></p>";
?>
