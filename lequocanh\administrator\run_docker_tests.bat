@echo off
REM Docker Test Runner for Thuộc Tính Update (Windows)
REM Usage: run_docker_tests.bat [container_name]

echo 🐳 DOCKER TEST RUNNER - THUỘC TÍNH UPDATE
echo ==========================================
echo.

REM Get container name (default to common names)
set CONTAINER_NAME=%1
if "%CONTAINER_NAME%"=="" set CONTAINER_NAME=php_container
if "%CONTAINER_NAME%"=="" set CONTAINER_NAME=web
if "%CONTAINER_NAME%"=="" set CONTAINER_NAME=apache
if "%CONTAINER_NAME%"=="" set CONTAINER_NAME=nginx

echo 🔍 Checking Docker container...

REM Check if Docker is running
docker ps >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running or not accessible!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Check if container exists and is running
docker ps | findstr /C:"%CONTAINER_NAME%" >nul
if errorlevel 1 (
    echo ❌ Container '%CONTAINER_NAME%' is not running!
    echo.
    echo Available containers:
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo.
    echo Usage: %0 [container_name]
    echo Example: %0 my_php_container
    pause
    exit /b 1
)

echo ✅ Container '%CONTAINER_NAME%' is running
echo.

echo 🚀 Starting test suite...
echo.

REM Test 1: Basic functionality
echo 🧪 Running Basic Functionality Test...
echo ----------------------------------------
docker exec -it %CONTAINER_NAME% php /var/www/html/administrator/docker_test_thuoctinh.php
set BASIC_TEST_RESULT=%errorlevel%
echo.
echo Exit code: %BASIC_TEST_RESULT%
echo ----------------------------------------
echo.

REM Test 2: AJAX endpoints
echo 🧪 Running AJAX Endpoints Test...
echo ----------------------------------------
docker exec -it %CONTAINER_NAME% php /var/www/html/administrator/docker_test_ajax.php
set AJAX_TEST_RESULT=%errorlevel%
echo.
echo Exit code: %AJAX_TEST_RESULT%
echo ----------------------------------------
echo.

REM Test 3: File structure and permissions
echo 🧪 Running File Structure Test...
echo ----------------------------------------
docker exec -it %CONTAINER_NAME% bash -c "cd /var/www/html/administrator && echo '📁 Checking file structure...' && echo '' && files=('elements_LQA/mthuoctinh/thuoctinhView.php' 'elements_LQA/mthuoctinh/thuoctinhUpdate.php' 'elements_LQA/mthuoctinh/thuoctinhAct.php' 'elements_LQA/mod/thuoctinhCls.php' 'elements_LQA/mod/database.php') && for file in \"${files[@]}\"; do if [ -f \"$file\" ]; then size=$(stat -c%%s \"$file\"); perms=$(stat -c%%a \"$file\"); echo \"✅ $file - Size: $size bytes, Permissions: $perms\"; else echo \"❌ $file - NOT FOUND\"; fi; done"
set FILE_TEST_RESULT=%errorlevel%
echo.
echo Exit code: %FILE_TEST_RESULT%
echo ----------------------------------------
echo.

REM Test 4: Database connectivity
echo 🧪 Running Database Connectivity Test...
echo ----------------------------------------
docker exec -it %CONTAINER_NAME% php -r "try { require_once '/var/www/html/administrator/elements_LQA/mod/database.php'; $db = Database::getInstance(); $conn = $db->getConnection(); if ($conn) { echo '✅ Database connection successful\n'; $stmt = $conn->query('SELECT COUNT(*) as count FROM thuoctinh'); $result = $stmt->fetch(PDO::FETCH_ASSOC); echo '✅ thuoctinh table accessible, records: ' . $result['count'] . '\n'; $stmt = $conn->query('SELECT idThuocTinh, tenThuocTinh FROM thuoctinh LIMIT 1'); $record = $stmt->fetch(PDO::FETCH_ASSOC); if ($record) { echo '✅ Sample record: ID=' . $record['idThuocTinh'] . ', Name=' . $record['tenThuocTinh'] . '\n'; } else { echo '⚠️  No records found in thuoctinh table\n'; } } else { echo '❌ Database connection failed\n'; exit(1); } } catch (Exception $e) { echo '❌ Database error: ' . $e->getMessage() . '\n'; exit(1); }"
set DB_TEST_RESULT=%errorlevel%
echo.
echo Exit code: %DB_TEST_RESULT%
echo ----------------------------------------
echo.

REM Summary
echo 📊 TEST SUITE SUMMARY
echo =====================
echo.

set /a total_tests=4
set /a passed_tests=0

echo Test Results:

if %BASIC_TEST_RESULT%==0 (
    echo ✅ Basic Functionality Test: PASSED
    set /a passed_tests+=1
) else (
    echo ❌ Basic Functionality Test: FAILED
)

if %AJAX_TEST_RESULT%==0 (
    echo ✅ AJAX Endpoints Test: PASSED
    set /a passed_tests+=1
) else (
    echo ❌ AJAX Endpoints Test: FAILED
)

if %FILE_TEST_RESULT%==0 (
    echo ✅ File Structure Test: PASSED
    set /a passed_tests+=1
) else (
    echo ❌ File Structure Test: FAILED
)

if %DB_TEST_RESULT%==0 (
    echo ✅ Database Connectivity Test: PASSED
    set /a passed_tests+=1
) else (
    echo ❌ Database Connectivity Test: FAILED
)

echo.
echo Summary: %passed_tests%/%total_tests% tests passed

REM Calculate percentage
set /a percentage=passed_tests*100/total_tests
echo Success rate: %percentage%%%

echo.
if %passed_tests%==%total_tests% (
    echo 🎉 ALL TESTS PASSED! Thuộc tính update functionality is ready to use.
    echo.
    echo You can now test the update functionality in your browser:
    echo http://localhost/administrator/index.php?req=thuoctinhview^&result=ok
    pause
    exit /b 0
) else if %passed_tests% geq 3 (
    echo ⚠️  MOSTLY WORKING with minor issues. Check failed tests above.
    pause
    exit /b 1
) else (
    echo 🚨 MULTIPLE ISSUES DETECTED. Please fix failed tests before using.
    pause
    exit /b 2
)
