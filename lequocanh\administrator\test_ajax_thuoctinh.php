<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX Thuộc Tính</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>🔍 TEST AJAX CẬP NHẬT THUỘC TÍNH</h1>
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h2>Test Form:</h2>
        <form id="test-form">
            <p>
                <label>ID Thuộc Tính:</label><br>
                <input type="number" name="idThuocTinh" value="7" required>
            </p>
            <p>
                <label>Tên <PERSON>:</label><br>
                <input type="text" name="tenThuocTinh" value="Test Update" required>
            </p>
            <p>
                <label>Ghi Chú:</label><br>
                <input type="text" name="ghiChu" value="Test ghi chú">
            </p>
            <p>
                <button type="submit">Test AJAX Update</button>
            </p>
        </form>
        
        <div id="result" style="margin-top: 20px; padding: 10px; border-radius: 5px;"></div>
    </div>
    
    <div style="background: #e7f3ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h2>Debug Info:</h2>
        <div id="debug-info"></div>
    </div>

    <script>
        $(document).ready(function() {
            $("#test-form").on("submit", function(e) {
                e.preventDefault();
                
                console.log("Form submitted");
                
                var formData = new FormData(this);
                formData.append("reqact", "updatethuoctinh");
                
                // Debug: log form data
                console.log("Form data:");
                for (var pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }
                
                $("#result").html('<div style="color: blue;">Đang gửi request...</div>');
                $("#debug-info").html('<p><strong>URL:</strong> ./elements_LQA/mthuoctinh/thuoctinhAct.php</p>');
                
                $.ajax({
                    url: "./elements_LQA/mthuoctinh/thuoctinhAct.php",
                    type: "POST",
                    data: formData,
                    contentType: false,
                    processData: false,
                    dataType: 'json',
                    success: function(response) {
                        console.log("Success response:", response);
                        
                        if (response.success) {
                            $("#result").html('<div style="background: #d4edda; color: #155724; padding: 10px; border-radius: 5px;">✅ ' + response.message + '</div>');
                        } else {
                            $("#result").html('<div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;">❌ ' + response.message + '</div>');
                        }
                        
                        $("#debug-info").append('<p><strong>Response:</strong> ' + JSON.stringify(response, null, 2) + '</p>');
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX Error:");
                        console.error("Status:", status);
                        console.error("Error:", error);
                        console.error("Response Text:", xhr.responseText);
                        console.error("Status Code:", xhr.status);
                        
                        var errorMsg = 'Lỗi AJAX';
                        if (xhr.status === 404) {
                            errorMsg = 'File không tồn tại (404)';
                        } else if (xhr.status === 500) {
                            errorMsg = 'Lỗi server (500)';
                        } else if (xhr.status === 0) {
                            errorMsg = 'Không thể kết nối';
                        }
                        
                        $("#result").html('<div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;">❌ ' + errorMsg + '</div>');
                        
                        $("#debug-info").append('<p><strong>Error Details:</strong></p>');
                        $("#debug-info").append('<p>Status: ' + status + '</p>');
                        $("#debug-info").append('<p>Error: ' + error + '</p>');
                        $("#debug-info").append('<p>Status Code: ' + xhr.status + '</p>');
                        $("#debug-info").append('<p>Response: ' + xhr.responseText + '</p>');
                    }
                });
            });
        });
    </script>
    
    <div style="margin: 20px 0;">
        <a href="index.php?req=thuoctinhview" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">📋 Quay lại quản lý thuộc tính</a>
    </div>
</body>
</html>
