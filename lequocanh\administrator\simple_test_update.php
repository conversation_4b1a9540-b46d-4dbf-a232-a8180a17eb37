<?php
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/thuoctinhCls.php';

// Test direct update
if (isset($_POST['direct_test'])) {
    echo "<h2>🔧 DIRECT UPDATE TEST</h2>";
    
    try {
        $thuoctinhObj = new ThuocTinh();
        
        // Get first attribute
        $db = Database::getInstance();
        $conn = $db->getConnection();
        $stmt = $conn->query("SELECT * FROM thuoctinh LIMIT 1");
        $item = $stmt->fetch(PDO::FETCH_OBJ);
        
        if ($item) {
            echo "<p><strong>Testing with:</strong></p>";
            echo "<ul>";
            echo "<li>ID: " . $item->idThuocTinh . "</li>";
            echo "<li>Current Name: " . htmlspecialchars($item->tenThuocTinh) . "</li>";
            echo "<li>Current Note: " . htmlspecialchars($item->ghiChu) . "</li>";
            echo "</ul>";
            
            // Test update
            $newName = $item->tenThuocTinh . " (Test " . date('H:i:s') . ")";
            $newNote = $item->ghiChu . " (Updated)";
            
            $result = $thuoctinhObj->thuoctinhUpdate($newName, $newNote, $item->hinhanh, $item->idThuocTinh);
            
            if ($result) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
                echo "✅ Update successful! Rows affected: " . $result;
                echo "</div>";
                
                // Verify update
                $updated = $thuoctinhObj->thuoctinhGetById($item->idThuocTinh);
                if ($updated) {
                    echo "<p><strong>After update:</strong></p>";
                    echo "<ul>";
                    echo "<li>Name: " . htmlspecialchars($updated->tenThuocTinh) . "</li>";
                    echo "<li>Note: " . htmlspecialchars($updated->ghiChu) . "</li>";
                    echo "</ul>";
                }
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
                echo "❌ Update failed or no changes made";
                echo "</div>";
            }
        } else {
            echo "<p>No attributes found in database</p>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ Error: " . $e->getMessage();
        echo "</div>";
    }
    
    echo "<hr>";
}

// Test AJAX endpoint simulation
if (isset($_POST['ajax_test'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $thuoctinhObj = new ThuocTinh();
        
        $idThuocTinh = $_POST['idThuocTinh'] ?? null;
        $tenThuocTinh = $_POST['tenThuocTinh'] ?? '';
        $ghiChu = $_POST['ghiChu'] ?? '';
        
        if (!$idThuocTinh) {
            echo json_encode([
                'success' => false,
                'message' => 'Missing ID'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        // Get current image
        $current = $thuoctinhObj->thuoctinhGetById($idThuocTinh);
        $hinhanh = $current ? $current->hinhanh : '';
        
        $result = $thuoctinhObj->thuoctinhUpdate($tenThuocTinh, $ghiChu, $hinhanh, $idThuocTinh);
        
        echo json_encode([
            'success' => $result > 0,
            'message' => $result > 0 ? 'Update successful!' : 'No changes made',
            'rows_affected' => $result
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Update Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>🧪 SIMPLE UPDATE TEST</h1>
    
    <div style="margin: 20px 0;">
        <h2>1. Direct PHP Test</h2>
        <form method="post">
            <button type="submit" name="direct_test" value="1">Run Direct Test</button>
        </form>
    </div>
    
    <div style="margin: 20px 0;">
        <h2>2. AJAX Test</h2>
        <button onclick="testAjax()">Run AJAX Test</button>
        <div id="ajax-result" style="margin-top: 10px;"></div>
    </div>
    
    <div style="margin: 20px 0;">
        <h2>3. Test Real Update Form</h2>
        <button onclick="testRealForm()">Test Real Update Form</button>
        <div id="real-form-result" style="margin-top: 10px;"></div>
    </div>

    <script>
    function testAjax() {
        $('#ajax-result').html('<div style="color: blue;">Testing AJAX...</div>');
        
        $.ajax({
            url: './simple_test_update.php',
            type: 'POST',
            data: {
                ajax_test: true,
                idThuocTinh: 1,
                tenThuocTinh: 'Test AJAX Update ' + new Date().getTime(),
                ghiChu: 'AJAX test note'
            },
            dataType: 'json',
            success: function(response) {
                console.log('AJAX Response:', response);
                
                if (response.success) {
                    $('#ajax-result').html('<div style="color: green;">✅ AJAX Success: ' + response.message + '</div>');
                } else {
                    $('#ajax-result').html('<div style="color: red;">❌ AJAX Failed: ' + response.message + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.error('Response:', xhr.responseText);
                $('#ajax-result').html('<div style="color: red;">❌ AJAX Error: ' + error + '<br>Response: ' + xhr.responseText + '</div>');
            }
        });
    }
    
    function testRealForm() {
        $('#real-form-result').html('<div style="color: blue;">Testing real form...</div>');
        
        // Test the actual thuoctinhAct.php endpoint
        var formData = new FormData();
        formData.append('reqact', 'updatethuoctinh');
        formData.append('idThuocTinh', '1');
        formData.append('tenThuocTinh', 'Real Form Test ' + new Date().getTime());
        formData.append('ghiChu', 'Real form test note');
        
        $.ajax({
            url: './elements_LQA/mthuoctinh/thuoctinhAct.php',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function(response) {
                console.log('Real Form Response:', response);
                
                if (response && response.success) {
                    $('#real-form-result').html('<div style="color: green;">✅ Real Form Success: ' + response.message + '</div>');
                } else {
                    $('#real-form-result').html('<div style="color: red;">❌ Real Form Failed: ' + (response ? response.message : 'No response') + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Real Form Error:', error);
                console.error('Status:', xhr.status);
                console.error('Response:', xhr.responseText);
                $('#real-form-result').html('<div style="color: red;">❌ Real Form Error: ' + error + ' (Status: ' + xhr.status + ')<br>Response: ' + xhr.responseText.substring(0, 200) + '</div>');
            }
        });
    }
    </script>
</body>
</html>
