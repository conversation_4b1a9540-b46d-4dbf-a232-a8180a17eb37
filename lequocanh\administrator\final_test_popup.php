<?php
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/thuoctinhCls.php';
?>

<!DOCTYPE html>
<html>
<head>
    <title>🎯 FINAL TEST - <PERSON><PERSON><PERSON><PERSON></title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        
        .iconimg { cursor: pointer; width: 24px; height: 24px; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        
        /* Copy popup styles từ thuoctinhView.php */
        #w_update_tt {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border: 2px solid #3498db;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            z-index: 9999;
            display: none;
            width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        #w_close_btn_tt {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #f44336;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10000;
            font-size: 18px;
        }
        
        #w_close_btn_tt:hover { background: #d32f2f; }
        
        .log-container {
            background: #000;
            color: #0f0;
            padding: 15px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎯 FINAL TEST - Thuộc Tính Popup</h1>
    
    <div class="test-section">
        <h2>📋 Test Data</h2>
        <?php
        try {
            $thuoctinhObj = new ThuocTinh();
            $db = Database::getInstance();
            $conn = $db->getConnection();
            $stmt = $conn->query("SELECT * FROM thuoctinh LIMIT 3");
            $thuoctinhList = $stmt->fetchAll(PDO::FETCH_OBJ);
            
            if ($thuoctinhList) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Tên thuộc tính</th><th>Ghi chú</th><th>Actions</th></tr>";
                foreach ($thuoctinhList as $item) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($item->idThuocTinh) . "</td>";
                    echo "<td>" . htmlspecialchars($item->tenThuocTinh) . "</td>";
                    echo "<td>" . htmlspecialchars($item->ghiChu) . "</td>";
                    echo "<td>";
                    // Giống như trong thuoctinhView.php
                    echo "<img src='./elements_LQA/img_LQA/Update.png' class='iconimg w_update_btn_open_tt' data-id='" . $item->idThuocTinh . "' alt='Update'>";
                    echo "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='error'>Không có dữ liệu thuộc tính</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>Lỗi: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🧪 Manual Tests</h2>
        <button class="btn btn-primary" onclick="testPopupShow()">Test Show Popup</button>
        <button class="btn btn-warning" onclick="testPopupHide()">Test Hide Popup</button>
        <button class="btn btn-success" onclick="testAjaxLoad(1)">Test AJAX Load (ID=1)</button>
        <button class="btn btn-danger" onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div class="test-section">
        <h2>📝 Console Logs</h2>
        <div id="console-logs" class="log-container"></div>
    </div>

    <!-- Container cho popup cập nhật thuộc tính - copy từ thuoctinhView.php -->
    <div id="w_update_tt">
        <div id="w_close_btn_tt">×</div>
        <div id="w_update_form_tt">
            <h3>Test Popup</h3>
            <p>Popup đang hoạt động!</p>
        </div>
    </div>

    <script>
    // Console logging system
    var logContainer = document.getElementById('console-logs');
    var originalLog = console.log;
    var originalError = console.error;
    
    function addLog(message, type = 'log') {
        var timestamp = new Date().toLocaleTimeString();
        var color = type === 'error' ? '#f00' : '#0f0';
        logContainer.innerHTML += '<div style="color: ' + color + '">[' + timestamp + '] ' + message + '</div>';
        logContainer.scrollTop = logContainer.scrollHeight;
        
        if (type === 'error') {
            originalError(message);
        } else {
            originalLog(message);
        }
    }
    
    console.log = function(message) { addLog(message, 'log'); };
    console.error = function(message) { addLog(message, 'error'); };
    
    function clearLogs() {
        logContainer.innerHTML = '';
    }
    
    // Manual test functions
    function testPopupShow() {
        console.log('Manual test: Showing popup');
        $("#w_update_tt").show();
    }
    
    function testPopupHide() {
        console.log('Manual test: Hiding popup');
        $("#w_update_tt").hide();
    }
    
    function testAjaxLoad(id) {
        console.log('Manual test: Loading AJAX for ID ' + id);
        $("#w_update_tt").show();
        $("#w_update_form_tt").html('<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
        
        $.ajax({
            url: "./elements_LQA/mthuoctinh/thuoctinhUpdate.php",
            type: "POST",
            data: { idThuocTinh: id },
            success: function(response) {
                console.log('Manual AJAX success');
                $("#w_update_form_tt").html(response);
            },
            error: function(xhr, status, error) {
                console.error('Manual AJAX error: ' + error);
                $("#w_update_form_tt").html('<div style="color: red; padding: 20px;">Error: ' + error + '</div>');
            }
        });
    }
    
    $(document).ready(function() {
        console.log('=== FINAL TEST INITIALIZED ===');
        console.log('jQuery version: ' + $.fn.jquery);
        
        // Ensure popup is hidden initially
        $("#w_update_tt").hide();
        
        // Remove any existing event handlers to avoid conflicts
        $(".w_update_btn_open_tt").off("click");
        
        // Add our event handler with namespace
        $(".w_update_btn_open_tt").on("click.finaltest", function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var id = $(this).data("id");
            console.log('=== UPDATE BUTTON CLICKED ===');
            console.log('ID: ' + id);
            console.log('Button element:', this);
            
            if (!id) {
                console.error('No ID found on button');
                alert("Không tìm thấy ID thuộc tính");
                return;
            }
            
            console.log('Showing popup...');
            $("#w_update_tt").show();
            
            console.log('Loading form content...');
            $("#w_update_form_tt").html('<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> Đang tải...</div>');
            
            $.ajax({
                url: "./elements_LQA/mthuoctinh/thuoctinhUpdate.php",
                type: "POST",
                data: { idThuocTinh: id },
                success: function(response) {
                    console.log('AJAX success - form loaded');
                    $("#w_update_form_tt").html(response);
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error: ' + error);
                    console.error('Status: ' + status);
                    console.error('Response: ' + xhr.responseText);
                    $("#w_update_form_tt").html('<div style="color: red; padding: 20px;">Lỗi: ' + error + '</div>');
                }
            });
        });
        
        // Close button handler
        $(document).on("click", "#w_close_btn_tt", function() {
            console.log('Close button clicked');
            $("#w_update_tt").hide();
        });
        
        // Click outside to close
        $(document).on("click", "#w_update_tt", function(e) {
            if (e.target === this) {
                console.log('Clicked outside popup - closing');
                $("#w_update_tt").hide();
            }
        });
        
        console.log('Event handlers attached');
        console.log('Update buttons found: ' + $(".w_update_btn_open_tt").length);
    });
    </script>
</body>
</html>
