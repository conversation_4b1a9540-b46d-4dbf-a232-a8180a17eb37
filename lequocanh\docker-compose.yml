services:
  web:
    build: .
    ports:
      - "3000:80"
    volumes:
      - ./:/var/www/html
    restart: always
    container_name: lequocanh_web
    depends_on:
      - mysql

  mysql:
    image: mysql:5.7
    restart: always
    container_name: lequocanh_mysql
    environment:
      MYSQL_ROOT_PASSWORD: pw
      MYSQL_DATABASE: trainingdb
    volumes:
      - db_data:/var/lib/mysql

  phpmyadmin:
    image: phpmyadmin:latest
    restart: always
    container_name: lequocanh_phpmyadmin
    ports:
      - "9000:80"
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
    depends_on:
      - mysql

volumes:
  db_data:
    name: lequocanh_data
