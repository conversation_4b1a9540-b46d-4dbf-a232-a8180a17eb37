<?php
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/thuoctinhCls.php';
?>

<!DOCTYPE html>
<html>
<head>
    <title>🔧 DEBUG THUỘC TÍNH POPUP</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .iconimg { cursor: pointer; width: 24px; height: 24px; }
        button { padding: 8px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        
        /* Popup styles - copy từ file gốc */
        #w_update_tt {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border: 2px solid #3498db;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            z-index: 9999;
            display: none;
            width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        #w_close_btn_tt {
            position: absolute;
            top: 10px;
            right: 15px;
            background-color: #ff4d4d;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <h1>🔧 DEBUG THUỘC TÍNH POPUP</h1>
    
    <div class="debug-section">
        <h2>1. Kiểm tra jQuery và Dependencies</h2>
        <div id="jquery-check"></div>
        <button class="btn-primary" onclick="checkDependencies()">Check Dependencies</button>
    </div>
    
    <div class="debug-section">
        <h2>2. Kiểm tra Files</h2>
        <div id="file-check">
            <?php
            $files_to_check = [
                'elements_LQA/mthuoctinh/thuoctinhUpdate.php',
                'elements_LQA/mthuoctinh/thuoctinhAct.php',
                'elements_LQA/mod/thuoctinhCls.php'
            ];
            
            foreach ($files_to_check as $file) {
                $exists = file_exists(__DIR__ . '/' . $file);
                $readable = $exists ? is_readable(__DIR__ . '/' . $file) : false;
                echo "<p><strong>$file:</strong> ";
                echo $exists ? "✅ Exists" : "❌ Not found";
                echo $readable ? " | ✅ Readable" : " | ❌ Not readable";
                echo "</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="debug-section">
        <h2>3. Test Data</h2>
        <?php
        try {
            $thuoctinhObj = new ThuocTinh();
            $db = Database::getInstance();
            $conn = $db->getConnection();
            $stmt = $conn->query("SELECT * FROM thuoctinh LIMIT 5");
            $thuoctinhList = $stmt->fetchAll(PDO::FETCH_OBJ);
            
            if ($thuoctinhList) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Tên</th><th>Ghi chú</th><th>Actions</th></tr>";
                foreach ($thuoctinhList as $item) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($item->idThuocTinh) . "</td>";
                    echo "<td>" . htmlspecialchars($item->tenThuocTinh) . "</td>";
                    echo "<td>" . htmlspecialchars($item->ghiChu) . "</td>";
                    echo "<td>";
                    echo "<img src='./elements_LQA/img_LQA/Update.png' class='iconimg w_update_btn_open_tt' data-id='" . $item->idThuocTinh . "' alt='Update'>";
                    echo "<button class='btn-warning' onclick='testPopup(" . $item->idThuocTinh . ")'>Test Popup</button>";
                    echo "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='error'>Không có dữ liệu thuộc tính</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>Lỗi: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>4. Event Handler Tests</h2>
        <button class="btn-success" onclick="testEventHandlers()">Test Event Handlers</button>
        <button class="btn-warning" onclick="showPopupManually()">Show Popup Manually</button>
        <button class="btn-danger" onclick="hidePopupManually()">Hide Popup Manually</button>
        <div id="event-test-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>5. Console Logs</h2>
        <div id="console-logs" style="background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto;"></div>
        <button class="btn-primary" onclick="clearLogs()">Clear Logs</button>
    </div>

    <!-- Container cho popup cập nhật thuộc tính -->
    <div id="w_update_tt">
        <div id="w_close_btn_tt">×</div>
        <div id="w_update_form_tt">
            <h3>Test Popup Content</h3>
            <p>Nếu bạn thấy nội dung này, popup đã hoạt động!</p>
        </div>
    </div>

    <script>
    // Override console.log để hiển thị trong page
    var originalLog = console.log;
    var originalError = console.error;
    var logContainer = null;
    
    function addLog(message, type = 'log') {
        if (!logContainer) logContainer = document.getElementById('console-logs');
        var timestamp = new Date().toLocaleTimeString();
        var color = type === 'error' ? '#f00' : '#0f0';
        logContainer.innerHTML += '<div style="color: ' + color + '">[' + timestamp + '] ' + message + '</div>';
        logContainer.scrollTop = logContainer.scrollHeight;
        
        // Also call original
        if (type === 'error') {
            originalError(message);
        } else {
            originalLog(message);
        }
    }
    
    console.log = function(message) { addLog(message, 'log'); };
    console.error = function(message) { addLog(message, 'error'); };
    
    function clearLogs() {
        document.getElementById('console-logs').innerHTML = '';
    }
    
    function checkDependencies() {
        var result = document.getElementById('jquery-check');
        var checks = [];
        
        // Check jQuery
        if (typeof jQuery !== 'undefined') {
            checks.push('✅ jQuery loaded (version: ' + jQuery.fn.jquery + ')');
        } else {
            checks.push('❌ jQuery not loaded');
        }
        
        // Check $ shorthand
        if (typeof $ !== 'undefined') {
            checks.push('✅ $ shorthand available');
        } else {
            checks.push('❌ $ shorthand not available');
        }
        
        result.innerHTML = checks.join('<br>');
    }
    
    function testEventHandlers() {
        console.log('=== Testing Event Handlers ===');
        
        var result = document.getElementById('event-test-result');
        var tests = [];
        
        // Test if update buttons exist
        var updateButtons = $('.w_update_btn_open_tt');
        tests.push('Update buttons found: ' + updateButtons.length);
        console.log('Update buttons found: ' + updateButtons.length);
        
        // Test if popup container exists
        var popup = $('#w_update_tt');
        tests.push('Popup container exists: ' + (popup.length > 0 ? 'YES' : 'NO'));
        console.log('Popup container exists: ' + (popup.length > 0 ? 'YES' : 'NO'));
        
        // Test if popup is hidden
        tests.push('Popup is hidden: ' + (popup.is(':hidden') ? 'YES' : 'NO'));
        console.log('Popup is hidden: ' + (popup.is(':hidden') ? 'YES' : 'NO'));
        
        result.innerHTML = tests.join('<br>');
    }
    
    function testPopup(id) {
        console.log('=== Testing Popup for ID: ' + id + ' ===');
        
        // Test manual popup opening
        console.log('Attempting to show popup...');
        $("#w_update_tt").show();
        
        // Test AJAX call
        console.log('Testing AJAX call to thuoctinhUpdate.php...');
        $.ajax({
            url: "./elements_LQA/mthuoctinh/thuoctinhUpdate.php",
            type: "POST",
            data: { idThuocTinh: id },
            success: function(response) {
                console.log('AJAX Success:', response);
                $("#w_update_form_tt").html(response);
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.error('Status:', status);
                console.error('Response:', xhr.responseText);
                $("#w_update_form_tt").html('<div style="color: red;">AJAX Error: ' + error + '</div>');
            }
        });
    }
    
    function showPopupManually() {
        console.log('Showing popup manually...');
        $("#w_update_tt").show();
    }
    
    function hidePopupManually() {
        console.log('Hiding popup manually...');
        $("#w_update_tt").hide();
    }
    
    $(document).ready(function() {
        console.log('=== Document Ready ===');
        console.log('jQuery version: ' + $.fn.jquery);
        
        // Test initial state
        console.log('Initial popup state: ' + ($("#w_update_tt").is(':hidden') ? 'hidden' : 'visible'));
        
        // Ensure popup is hidden
        $("#w_update_tt").hide();
        
        // Add event handler with debugging
        $(".w_update_btn_open_tt").on("click", function() {
            var id = $(this).data("id");
            console.log('=== Update Button Clicked ===');
            console.log('Button clicked for ID:', id);
            console.log('Button element:', this);
            
            if (!id) {
                console.error('No ID found on button');
                alert("Không tìm thấy ID thuộc tính");
                return;
            }
            
            console.log('Showing popup...');
            $("#w_update_tt").show();
            
            console.log('Loading form...');
            $("#w_update_form_tt").html('<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> Đang tải...</div>');
            
            $.ajax({
                url: "./elements_LQA/mthuoctinh/thuoctinhUpdate.php",
                type: "POST",
                data: { idThuocTinh: id },
                success: function(response) {
                    console.log('Form loaded successfully');
                    $("#w_update_form_tt").html(response);
                },
                error: function(xhr, status, error) {
                    console.error('Error loading form:', error);
                    $("#w_update_form_tt").html('<div style="color: red; padding: 20px;">Lỗi khi tải form: ' + error + '</div>');
                }
            });
        });
        
        // Close button handler
        $(document).on("click", "#w_close_btn_tt", function() {
            console.log('Close button clicked');
            $("#w_update_tt").hide();
        });
        
        console.log('Event handlers attached');
    });
    </script>
</body>
</html>
