<?php
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/thuoctinhCls.php';

// Test direct update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_update'])) {
    $thuoctinhObj = new ThuocTinh();
    $result = $thuoctinhObj->thuoctinhUpdate(
        $_POST['tenThuocTinh'],
        $_POST['ghiChu'], 
        '', // no image
        $_POST['idThuocTinh']
    );
    
    echo "<div style='padding: 20px; background: " . ($result ? '#d4edda' : '#f8d7da') . "; margin: 20px; border-radius: 5px;'>";
    echo $result ? "✅ Cập nhật thành công! Rows affected: $result" : "❌ Cập nhật thất bại!";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>🧪 Test Update Thuộc Tính</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🧪 Test Update Thuộc Tính</h1>
    
    <div class="test-section">
        <h2>📋 Current Data</h2>
        <?php
        try {
            $thuoctinhObj = new ThuocTinh();
            $db = Database::getInstance();
            $conn = $db->getConnection();
            $stmt = $conn->query("SELECT * FROM thuoctinh LIMIT 5");
            $thuoctinhList = $stmt->fetchAll(PDO::FETCH_OBJ);
            
            if ($thuoctinhList) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Tên</th><th>Ghi chú</th><th>Action</th></tr>";
                foreach ($thuoctinhList as $item) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($item->idThuocTinh) . "</td>";
                    echo "<td>" . htmlspecialchars($item->tenThuocTinh) . "</td>";
                    echo "<td>" . htmlspecialchars($item->ghiChu) . "</td>";
                    echo "<td><button class='btn btn-warning' onclick='loadForEdit(" . $item->idThuocTinh . ")'>Edit</button></td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>Lỗi: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>✏️ Direct Update Test</h2>
        <form method="POST">
            <div class="form-group">
                <label>ID Thuộc Tính:</label>
                <input type="number" name="idThuocTinh" id="edit_id" required>
            </div>
            <div class="form-group">
                <label>Tên Thuộc Tính:</label>
                <input type="text" name="tenThuocTinh" id="edit_ten" required>
            </div>
            <div class="form-group">
                <label>Ghi Chú:</label>
                <input type="text" name="ghiChu" id="edit_ghichu">
            </div>
            <button type="submit" name="test_update" class="btn btn-primary">Test Direct Update</button>
        </form>
    </div>
    
    <div class="test-section">
        <h2>🔄 AJAX Update Test</h2>
        <form id="ajax-form">
            <div class="form-group">
                <label>ID Thuộc Tính:</label>
                <input type="number" name="idThuocTinh" id="ajax_id" required>
            </div>
            <div class="form-group">
                <label>Tên Thuộc Tính:</label>
                <input type="text" name="tenThuocTinh" id="ajax_ten" required>
            </div>
            <div class="form-group">
                <label>Ghi Chú:</label>
                <input type="text" name="ghiChu" id="ajax_ghichu">
            </div>
            <button type="submit" class="btn btn-success">Test AJAX Update</button>
            <div id="ajax-result" style="margin-top: 15px;"></div>
        </form>
    </div>
    
    <div class="test-section">
        <h2>📝 Console Logs</h2>
        <div id="console-logs" style="background: #000; color: #0f0; padding: 15px; font-family: monospace; height: 200px; overflow-y: auto;"></div>
        <button class="btn btn-warning" onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
    // Console logging
    var logContainer = document.getElementById('console-logs');
    var originalLog = console.log;
    var originalError = console.error;
    
    function addLog(message, type = 'log') {
        var timestamp = new Date().toLocaleTimeString();
        var color = type === 'error' ? '#f00' : '#0f0';
        logContainer.innerHTML += '<div style="color: ' + color + '">[' + timestamp + '] ' + message + '</div>';
        logContainer.scrollTop = logContainer.scrollHeight;
        
        if (type === 'error') {
            originalError(message);
        } else {
            originalLog(message);
        }
    }
    
    console.log = function(message) { addLog(message, 'log'); };
    console.error = function(message) { addLog(message, 'error'); };
    
    function clearLogs() {
        logContainer.innerHTML = '';
    }
    
    function loadForEdit(id) {
        console.log('Loading data for ID: ' + id);
        
        // Load data for both forms
        $.ajax({
            url: "./elements_LQA/mthuoctinh/thuoctinhUpdate.php",
            type: "POST",
            data: { idThuocTinh: id },
            success: function(response) {
                console.log('Data loaded successfully');
                // Extract data from response (this is a hack for demo)
                var parser = new DOMParser();
                var doc = parser.parseFromString(response, 'text/html');
                
                var tenInput = doc.querySelector('input[name="tenThuocTinh"]');
                var ghiChuInput = doc.querySelector('input[name="ghiChu"]');
                
                if (tenInput && ghiChuInput) {
                    // Fill direct form
                    document.getElementById('edit_id').value = id;
                    document.getElementById('edit_ten').value = tenInput.value;
                    document.getElementById('edit_ghichu').value = ghiChuInput.value;
                    
                    // Fill AJAX form
                    document.getElementById('ajax_id').value = id;
                    document.getElementById('ajax_ten').value = tenInput.value;
                    document.getElementById('ajax_ghichu').value = ghiChuInput.value;
                    
                    console.log('Forms populated with data');
                } else {
                    console.error('Could not extract data from response');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading data: ' + error);
            }
        });
    }
    
    $(document).ready(function() {
        console.log('=== TEST PAGE INITIALIZED ===');
        
        // AJAX form handler
        $("#ajax-form").on("submit", function(e) {
            e.preventDefault();
            
            console.log('=== AJAX FORM SUBMIT ===');
            
            var formData = new FormData(this);
            
            // Log form data
            console.log('Form data:');
            for (var pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }
            
            $('#ajax-result').html('<span style="color: blue;">Đang xử lý...</span>');
            
            $.ajax({
                url: "./elements_LQA/mthuoctinh/thuoctinhAct.php?reqact=updatethuoctinh",
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(response) {
                    console.log('AJAX Success:', response);
                    
                    if (response && response.success) {
                        $('#ajax-result').html('<span style="color: green;">✅ ' + response.message + '</span>');
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $('#ajax-result').html('<span style="color: red;">❌ ' + (response.message || 'Update failed') + '</span>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);
                    
                    $('#ajax-result').html('<span style="color: red;">❌ Error: ' + error + '</span>');
                }
            });
        });
        
        console.log('Event handlers attached');
    });
    </script>
</body>
</html>
