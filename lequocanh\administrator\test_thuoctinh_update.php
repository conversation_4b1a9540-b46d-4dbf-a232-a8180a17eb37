<?php
session_start();
require_once 'elements_LQA/mod/database.php';
require_once 'elements_LQA/mod/thuoctinhCls.php';

echo "<h1>🔍 TEST CẬP NHẬT THUỘC TÍNH</h1>";

try {
    $thuoctinhObj = new ThuocTinh();
    
    // L<PERSON>y danh sách thuộc tính
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>1. DANH SÁCH THUỘC TÍNH:</h2>";
    
    $stmt = $conn->query("SELECT idThuocTinh, tenThuocTinh, ghiChu FROM thuoctinh LIMIT 5");
    $thuoctinhList = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($thuoctinhList)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Tên thuộc tính</th>";
        echo "<th style='padding: 8px;'>Ghi chú</th>";
        echo "<th style='padding: 8px;'>Test update</th>";
        echo "</tr>";
        
        foreach ($thuoctinhList as $thuoctinh) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $thuoctinh['idThuocTinh'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($thuoctinh['tenThuocTinh']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($thuoctinh['ghiChu']) . "</td>";
            echo "<td style='padding: 8px;'>";
            echo "<a href='test_thuoctinh_update.php?test_update=" . $thuoctinh['idThuocTinh'] . "' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test update</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Không có thuộc tính nào</p>";
    }
    echo "</div>";
    
    // Test update nếu có tham số
    if (isset($_GET['test_update'])) {
        $idThuocTinh = intval($_GET['test_update']);
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h2>2. TEST UPDATE THUỘC TÍNH ID: $idThuocTinh</h2>";
        
        // Lấy thông tin thuộc tính
        $thuoctinhInfo = $thuoctinhObj->thuoctinhGetbyId($idThuocTinh);
        if ($thuoctinhInfo) {
            echo "<p><strong>Tên thuộc tính:</strong> " . htmlspecialchars($thuoctinhInfo->tenThuocTinh) . "</p>";
            echo "<p><strong>Ghi chú:</strong> " . htmlspecialchars($thuoctinhInfo->ghiChu) . "</p>";
            
            // Form test update
            echo "<form method='post' style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
            echo "<h3>Form test update:</h3>";
            echo "<input type='hidden' name='test_update_submit' value='1'>";
            echo "<input type='hidden' name='idThuocTinh' value='$idThuocTinh'>";
            echo "<p><label>Tên thuộc tính mới:</label><br>";
            echo "<input type='text' name='tenThuocTinh' value='" . htmlspecialchars($thuoctinhInfo->tenThuocTinh) . " (Updated)' style='width: 300px; padding: 5px;'></p>";
            echo "<p><label>Ghi chú mới:</label><br>";
            echo "<input type='text' name='ghiChu' value='" . htmlspecialchars($thuoctinhInfo->ghiChu) . " (Updated)' style='width: 300px; padding: 5px;'></p>";
            echo "<p><input type='submit' value='Test Update' style='background: #007bff; color: white; padding: 8px 15px; border: none; border-radius: 3px; cursor: pointer;'></p>";
            echo "</form>";
        } else {
            echo "<p>❌ Không tìm thấy thuộc tính với ID: $idThuocTinh</p>";
        }
        echo "</div>";
    }
    
    // Xử lý test update
    if (isset($_POST['test_update_submit'])) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h2>3. KẾT QUẢ TEST UPDATE:</h2>";
        
        $idThuocTinh = intval($_POST['idThuocTinh']);
        $tenThuocTinh = $_POST['tenThuocTinh'];
        $ghiChu = $_POST['ghiChu'];
        $hinhanh = ''; // Giữ hình ảnh cũ
        
        // Lấy hình ảnh cũ
        $thuoctinhInfo = $thuoctinhObj->thuoctinhGetbyId($idThuocTinh);
        if ($thuoctinhInfo) {
            $hinhanh = $thuoctinhInfo->hinhanh;
        }
        
        echo "<p><strong>Dữ liệu gửi:</strong></p>";
        echo "<ul>";
        echo "<li>ID: $idThuocTinh</li>";
        echo "<li>Tên: " . htmlspecialchars($tenThuocTinh) . "</li>";
        echo "<li>Ghi chú: " . htmlspecialchars($ghiChu) . "</li>";
        echo "</ul>";
        
        try {
            $result = $thuoctinhObj->thuoctinhUpdate($tenThuocTinh, $ghiChu, $hinhanh, $idThuocTinh);
            
            if ($result) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
                echo "✅ Cập nhật thành công! Số dòng bị ảnh hưởng: $result";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
                echo "❌ Cập nhật thất bại hoặc không có thay đổi";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
            echo "❌ Lỗi: " . $e->getMessage();
            echo "</div>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ Lỗi: " . $e->getMessage() . "</h3>";
    echo "</div>";
}

echo "<div style='margin: 20px 0;'>";
echo "<a href='index.php?req=thuoctinhview' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📋 Quay lại quản lý thuộc tính</a>";
echo "</div>";
?>
