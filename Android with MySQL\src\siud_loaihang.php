<?php
// Disable error reporting for production
error_reporting(0);
ini_set('display_errors', 0);

// Set headers
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// Log function for debugging (disabled for production)
function logMessage($message)
{
    // Logging disabled to prevent file permission errors
    return;
}

// Log incoming request
logMessage("=== NEW REQUEST ===");
logMessage("Method: " . $_SERVER['REQUEST_METHOD']);
logMessage("URI: " . $_SERVER['REQUEST_URI']);

try {
    // Get and log input data
    $input = file_get_contents('php://input');
    logMessage("Raw input: " . $input);

    $obj = json_decode($input);
    if (json_last_error() !== JSON_ERROR_NONE && $_SERVER['REQUEST_METHOD'] == 'POST') {
        logMessage("JSON decode error: " . json_last_error_msg());
        throw new Exception("Invalid JSON data");
    }

    // Database connection options
    $opt = array(
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    );

    // Try to connect to database
    $connect = new PDO("mysql:host=mysql;dbname=android_db;charset=utf8", "root", "android123", $opt);
    logMessage("Database connection successful");

    // Handle different HTTP methods
    if ($_SERVER['REQUEST_METHOD'] == "POST") {
        logMessage("Processing POST request");

        // Validate input data
        if (!$obj) {
            throw new Exception("No data received");
        }

        if (!isset($obj->tenloai)) {
            throw new Exception("Missing 'tenloai' field");
        }

        $tenloai = trim($obj->tenloai);
        if (empty($tenloai)) {
            throw new Exception("'tenloai' cannot be empty");
        }

        $hinhanhB64 = isset($obj->hinhanh) ? $obj->hinhanh : '';
        $hinhanhBin = '';

        // Detailed image processing
        logMessage("=== IMAGE PROCESSING ===");
        logMessage("Base64 received: " . (empty($hinhanhB64) ? "empty" : "length=" . strlen($hinhanhB64)));

        if (!empty($hinhanhB64)) {
            // Remove data URL prefix if present (data:image/jpeg;base64,)
            if (strpos($hinhanhB64, 'data:image') === 0) {
                $hinhanhB64 = substr($hinhanhB64, strpos($hinhanhB64, ',') + 1);
                logMessage("Removed data URL prefix");
            }

            // Clean base64 string
            $hinhanhB64 = str_replace([' ', '\n', '\r', '\t'], '', $hinhanhB64);
            logMessage("Cleaned base64 length: " . strlen($hinhanhB64));

            // Validate base64
            if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $hinhanhB64)) {
                logMessage("Invalid base64 format");
                throw new Exception("Invalid image format");
            }

            $hinhanhBin = base64_decode($hinhanhB64);
            if ($hinhanhBin === false) {
                logMessage("Base64 decode failed for image");
                throw new Exception("Failed to decode image data");
            }

            logMessage("Decoded binary size: " . strlen($hinhanhBin) . " bytes");

            // Check if it's a valid image
            $imageInfo = getimagesizefromstring($hinhanhBin);
            if ($imageInfo === false) {
                logMessage("Invalid image data - not a valid image file");
                throw new Exception("Invalid image file");
            }

            logMessage("Image info: " . $imageInfo[0] . "x" . $imageInfo[1] . ", type: " . $imageInfo['mime']);

            // Check file size (max 16MB for LONGBLOB)
            if (strlen($hinhanhBin) > 16777215) {
                logMessage("Image too large: " . strlen($hinhanhBin) . " bytes");
                throw new Exception("Image file too large (max 16MB)");
            }
        } else {
            logMessage("No image data provided");
        }

        logMessage("Data to insert: tenloai='$tenloai', image_size=" . strlen($hinhanhBin));

        $sql = "INSERT INTO loaihang(tenloai, hinhanh) VALUES(?,?)";
        $insert = $connect->prepare($sql);
        $data = array($tenloai, $hinhanhBin);
        $ok = $insert->execute($data);

        $response = array("result" => $ok ? "Insert OK" : "Insert Fail");
        logMessage("Insert result: " . ($ok ? "SUCCESS" : "FAILED"));

        echo json_encode($response);
    }

    if ($_SERVER['REQUEST_METHOD'] == "PUT") {
        logMessage("Processing PUT request");

        if (!$obj || !isset($obj->tenloai) || !isset($obj->idloaihang)) {
            throw new Exception("Missing required fields for update");
        }

        $tenloai = trim($obj->tenloai);
        if (empty($tenloai)) {
            throw new Exception("'tenloai' cannot be empty");
        }

        $idloaihang = $obj->idloaihang;
        logMessage("Updating item ID: $idloaihang with tenloai: '$tenloai'");

        // Handle image update
        $updateImage = false;
        $hinhanhBin = null;

        if (isset($obj->hinhanh)) {
            $hinhanhB64 = $obj->hinhanh;

            logMessage("=== UPDATE IMAGE PROCESSING ===");
            logMessage("Base64 received: " . (empty($hinhanhB64) ? "empty" : "length=" . strlen($hinhanhB64)));

            if (!empty($hinhanhB64)) {
                // Remove data URL prefix if present
                if (strpos($hinhanhB64, 'data:image') === 0) {
                    $hinhanhB64 = substr($hinhanhB64, strpos($hinhanhB64, ',') + 1);
                    logMessage("Removed data URL prefix");
                }

                // Clean base64 string
                $hinhanhB64 = str_replace([' ', '\n', '\r', '\t'], '', $hinhanhB64);
                logMessage("Cleaned base64 length: " . strlen($hinhanhB64));

                // Validate base64
                if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $hinhanhB64)) {
                    logMessage("Invalid base64 format");
                    throw new Exception("Invalid image format");
                }

                $hinhanhBin = base64_decode($hinhanhB64);
                if ($hinhanhBin === false) {
                    logMessage("Base64 decode failed for image");
                    throw new Exception("Failed to decode image data");
                }

                logMessage("Decoded binary size: " . strlen($hinhanhBin) . " bytes");

                // Check if it's a valid image
                $imageInfo = getimagesizefromstring($hinhanhBin);
                if ($imageInfo === false) {
                    logMessage("Invalid image data - not a valid image file");
                    throw new Exception("Invalid image file");
                }

                logMessage("Image info: " . $imageInfo[0] . "x" . $imageInfo[1] . ", type: " . $imageInfo['mime']);

                // Check file size (max 16MB for LONGBLOB)
                if (strlen($hinhanhBin) > 16777215) {
                    logMessage("Image too large: " . strlen($hinhanhBin) . " bytes");
                    throw new Exception("Image file too large (max 16MB)");
                }

                $updateImage = true;
                logMessage("Image will be updated");
            } else {
                logMessage("Empty image data - image will not be updated");
            }
        } else {
            logMessage("No image field provided - image will not be updated");
        }

        // Prepare SQL based on whether image should be updated
        if ($updateImage) {
            $sql = "UPDATE loaihang SET tenloai = ?, hinhanh = ? WHERE idloaihang = ?";
            $data = array($tenloai, $hinhanhBin, $idloaihang);
            logMessage("Updating both name and image");
        } else {
            $sql = "UPDATE loaihang SET tenloai = ? WHERE idloaihang = ?";
            $data = array($tenloai, $idloaihang);
            logMessage("Updating name only");
        }

        $update = $connect->prepare($sql);
        $kq = $update->execute($data);

        $response = array("result" => $kq);
        logMessage("Update result: " . ($kq ? "SUCCESS" : "FAILED"));

        if ($kq) {
            // Get updated record for verification
            $stmt = $connect->prepare("SELECT tenloai, LENGTH(hinhanh) as image_size FROM loaihang WHERE idloaihang = ?");
            $stmt->execute([$idloaihang]);
            $updated = $stmt->fetch(PDO::FETCH_ASSOC);
            logMessage("Updated record: name='{$updated['tenloai']}', image_size={$updated['image_size']} bytes");
        }

        echo json_encode($response);
    }

    if ($_SERVER['REQUEST_METHOD'] == "GET") {
        logMessage("Processing GET request");

        if (isset($_GET['ID'])) {
            $ID = $_GET['ID'];
            logMessage("Getting item with ID: $ID");

            $sql = "SELECT idloaihang, tenloai, COALESCE(TO_BASE64(hinhanh), '') as hinhanh FROM loaihang WHERE idloaihang = ?";
            $select = $connect->prepare($sql);
            $data = array($ID);
            $select->setFetchMode(PDO::FETCH_OBJ);
            $select->execute($data);
            $obj_get = $select->fetch();

            logMessage("Retrieved item: " . ($obj_get ? "found" : "not found"));
            echo json_encode($obj_get);
        } else {
            logMessage("Getting all items");

            $sql = "SELECT idloaihang, tenloai, COALESCE(TO_BASE64(hinhanh), '') as hinhanh FROM loaihang";
            $select = $connect->prepare($sql);
            $select->setFetchMode(PDO::FETCH_OBJ);
            $select->execute();
            $obj_get_list = $select->fetchAll();

            logMessage("Retrieved " . count($obj_get_list) . " items");
            echo json_encode($obj_get_list);
        }
    }

    if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
        logMessage("Processing DELETE request");

        if (!isset($_GET['IDLOAIHANG'])) {
            throw new Exception("Missing IDLOAIHANG parameter");
        }

        $idloaihang = $_GET['IDLOAIHANG'];
        logMessage("Deleting item with ID: $idloaihang");

        $sql = "DELETE FROM loaihang WHERE idloaihang = ?";
        $delete = $connect->prepare($sql);
        $data = array($idloaihang);
        $kq = $delete->execute($data);

        $result = $kq ? "Delete OK" : "Delete not OK";
        logMessage("Delete result: $result");
        echo $result;
    }
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    logMessage("PDO Error: " . $error);
    http_response_code(500);
    echo json_encode(array("error" => $error));
} catch (Exception $e) {
    $error = "Error: " . $e->getMessage();
    logMessage("General Error: " . $error);
    http_response_code(400);
    echo json_encode(array("error" => $error));
}

logMessage("=== END REQUEST ===\n");
